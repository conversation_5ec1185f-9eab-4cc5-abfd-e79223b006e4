TypeScript/ESLint Error Debugging Guide: Step-by-Step Approach
Phase 1: Error Identification and Context Gathering
Step 1: Read the Complete Error Message

Copy the full error message - don't truncate or paraphrase
Note the error code (e.g., TS2304, @typescript-eslint/no-unused-vars)
Identify the file path and line number where the error occurs
Record any additional context provided in the error message

Step 2: Locate the Exact Error Location

Navigate to the specific file and line number
Examine the surrounding code (at least 5-10 lines above and below)
Identify the specific token or expression causing the error
Note the function/class/module context where the error occurs

Step 3: Categorize the Error Type
Determine if it's:

Type-related: Type mismatches, missing types, inference issues
Import/Export: Module resolution, missing imports, circular dependencies
Syntax: Incorrect TypeScript syntax, language feature usage
Linting: Code style, best practices, potential bugs
Configuration: tsconfig.json, ESLint config issues

Phase 2: Root Cause Analysis
Step 4: Understand WHY the Error Exists
For TypeScript Errors:

Check type definitions: What types are expected vs. what's provided?
Trace data flow: Follow the variable/function from declaration to usage
Examine type inference: Is TypeScript inferring the wrong type?
Review generics: Are type parameters correctly defined and used?
Check module boundaries: Are types properly exported/imported?

For ESLint Errors:

Understand the rule: What is the linting rule trying to prevent?
Identify the violation: How does the current code violate the rule?
Consider rule purpose: Is this about performance, readability, or bug prevention?
Check rule configuration: Is the rule configured correctly for your project?

Step 5: Investigate Dependencies and Context

Check related imports: Are all necessary modules imported?
Verify package versions: Are there version conflicts or missing packages?
Review configuration files: tsconfig.json, .eslintrc, package.json
Examine type definitions: @types packages, .d.ts files
Consider environment: Node.js vs. browser, different TypeScript versions

Step 6: Reproduce and Isolate

Create a minimal reproduction if possible
Test in isolation: Remove unrelated code to focus on the core issue
Verify the error persists in the simplified context
Document your findings about what specifically triggers the error

Phase 3: Solution Strategy
Step 7: Evaluate Solution Approaches
Before implementing a fix, consider:
Option A: Fix the Root Cause

Correct the type definitions
Adjust the implementation to match expected types
Fix the import/export statements
Update configuration if needed

Option B: Adjust Type Definitions

Add proper type annotations
Create custom type definitions
Use type assertions (carefully and sparingly)
Implement type guards for runtime type checking

Option C: Configuration Changes

Modify tsconfig.json compiler options
Adjust ESLint rules or add exceptions
Update environment or target settings
Add or modify type declarations

Option D: Refactor for Better Design

Restructure code to eliminate type conflicts
Improve separation of concerns
Use better TypeScript patterns
Apply design patterns that work well with TypeScript

Step 8: Choose the Best Solution
Consider:

Correctness: Does it actually solve the problem?
Safety: Does it introduce new risks or hide real issues?
Maintainability: Will future developers understand this?
Performance: Are there performance implications?
Team standards: Does it align with project conventions?

Phase 4: Implementation and Verification
Step 9: Implement the Fix

Make the minimal change necessary to fix the error
Avoid over-engineering or fixing unrelated issues
Follow project coding standards
Add comments explaining complex type logic if needed
Consider backward compatibility

Step 10: Verify the Solution

Confirm the original error is resolved
Run the full TypeScript compiler (tsc --noEmit)
Execute ESLint on the affected files
Run relevant tests to ensure functionality isn't broken
Check for new errors introduced by the fix

Step 11: Test Edge Cases

Test with different input types if applicable
Verify null/undefined handling
Check array/object edge cases
Test async/Promise scenarios if relevant
Validate error boundaries

Phase 5: Documentation and Learning
Step 12: Document Your Solution

Write clear commit messages explaining the fix
Add inline comments for complex type logic
Update documentation if the fix changes APIs
Create or update type definitions if needed

Step 13: Learn and Prevent

Understand what you learned from solving this error
Identify patterns that led to the error
Consider preventive measures: linting rules, types, tests
Share knowledge with team members if applicable
Update project guidelines to prevent similar issues

Common Error Patterns and Investigation Tips
Type Errors (TS2xxx)
Investigation checklist:
□ What types are involved?
□ Where are these types defined?
□ Is there a type mismatch in assignment?
□ Are generic constraints satisfied?
□ Is null/undefined handling correct?
Import/Module Errors
Investigation checklist:
□ Does the imported module exist?
□ Is the import path correct?
□ Are there circular dependencies?
□ Is the module properly exported?
□ Are type definitions available?
ESLint Rule Violations
Investigation checklist:
□ What is the specific rule being violated?
□ Why does this rule exist?
□ Is this a false positive?
□ Should the rule be configured differently?
□ Is the code actually problematic?
Example Application
When you encounter an error like:
TS2339: Property 'foo' does not exist on type 'Bar'.
Follow this process:

Locate: Find where 'foo' is being accessed on type 'Bar'
Analyze: Check if 'Bar' should have property 'foo' or if wrong type is used
Trace: Follow the data flow to understand how 'Bar' got this type
Solution: Either add 'foo' to 'Bar' type, or use correct type/property
Verify: Ensure the fix doesn't break other code expecting the old behavior

Key Principles

Never ignore errors without understanding them
Avoid any types as quick fixes unless absolutely necessary
Understand before fixing - rushing leads to more problems
Test thoroughly after implementing fixes
Learn from each error to prevent similar issues in the future