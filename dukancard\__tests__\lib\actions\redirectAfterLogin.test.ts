import { getPostLoginRedirectPath } from "@/lib/actions/redirectAfterLogin";
import { SupabaseClient } from "@supabase/supabase-js";

const mockSupabase = {
  from: jest.fn().mockReturnThis(),
  select: jest.fn().mockReturnThis(),
  eq: jest.fn(),
};

describe("getPostLoginRedirectPath", () => {
  const userId = "test-user-id";
  let consoleErrorSpy: jest.SpyInstance;

  beforeEach(() => {
    jest.clearAllMocks();
    consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    consoleErrorSpy.mockRestore();
  });

  it("should return '/dashboard/customer' if a customer profile exists", async () => {
    (mockSupabase.eq as jest.Mock)
      .mockResolvedValueOnce({ data: [{ id: userId }], error: null }) // customer_profiles
      .mockResolvedValueOnce({ data: [], error: null }); // business_profiles

    const path = await getPostLoginRedirectPath(userId);
    expect(path).toBe("/dashboard/customer");
  });

  it("should return '/dashboard/business' if a business profile exists with a slug", async () => {
    (mockSupabase.eq as jest.Mock)
      .mockResolvedValueOnce({ data: [], error: null }) // customer_profiles
      .mockResolvedValueOnce({ data: [{ id: userId, business_slug: "test-slug" }], error: null }); // business_profiles

    const path = await getPostLoginRedirectPath(userId);
    expect(path).toBe("/dashboard/business");
  });

  it("should return '/onboarding' if a business profile exists without a slug", async () => {
    (mockSupabase.eq as jest.Mock)
      .mockResolvedValueOnce({ data: [], error: null }) // customer_profiles
      .mockResolvedValueOnce({ data: [{ id: userId, business_slug: null }], error: null }); // business_profiles

    const path = await getPostLoginRedirectPath(userId);
    expect(path).toBe("/onboarding");
  });

  it("should return '/choose-role' if no profile exists", async () => {
    (mockSupabase.eq as jest.Mock)
      .mockResolvedValueOnce({ data: [], error: null }) // customer_profiles
      .mockResolvedValueOnce({ data: [], error: null }); // business_profiles

    const path = await getPostLoginRedirectPath(userId);
    expect(path).toBe("/choose-role");
  });

  it("should return '/choose-role' on 'no rows found' error", async () => {
    (mockSupabase.eq as jest.Mock)
      .mockResolvedValueOnce({ data: [], error: { code: "PGRST116", message: "No rows found" } }) // customer_profiles
      .mockResolvedValueOnce({ data: [], error: { code: "PGRST116", message: "No rows found" } }); // business_profiles

    const path = await getPostLoginRedirectPath(userId);
    expect(path).toBe("/choose-role");
  });

  it("should return '/?view=home' on critical supabase error", async () => {
    (mockSupabase.eq as jest.Mock)
      .mockResolvedValueOnce({ data: null, error: new Error("Critical Error") }) // customer_profiles
      .mockResolvedValueOnce({ data: null, error: new Error("Critical Error") }); // business_profiles

    const path = await getPostLoginRedirectPath(userId);
    expect(path).toBe("/?view=home");
  });

  it("should return '/?view=home' on unexpected error", async () => {
    (mockSupabase.eq as jest.Mock).mockRejectedValueOnce(new Error("Unexpected error"));
    const path = await getPostLoginRedirectPath(userId);
    expect(path).toBe("/?view=home");
  });
});