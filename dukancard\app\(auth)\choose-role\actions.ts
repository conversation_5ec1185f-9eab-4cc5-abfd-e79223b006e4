
import { redirect } from "next/navigation";
import { revalidatePath } from "next/cache";
import { checkIfCustomerProfileExists, createUserProfile } from "@/lib/supabase/services/customerService";
import { getAuthenticatedUser } from "@/lib/supabase/services/sharedService";
import { createClient } from "@/utils/supabase/server";
import { COLUMNS } from "@/lib/supabase/constants";

export async function createCustomerProfileAction(
  userId: string,
  redirectSlug: string | null = null,
  _message: string | null = null
) {
  const supabase = await createClient();
  if (!userId) {
    return { error: "User ID is required." };
  }

  // Fetch user details first
  const { user, error: userError } = await getAuthenticatedUser(supabase);

  if (userError || !user) {
    // Error fetching user in action
    return { error: "User not found or authentication error." };
  }

  // Check if customer profile already exists
  const { exists, error: checkError } = await checkIfCustomerProfileExists(supabase, userId);

  if (checkError) {
    return { error: checkError };
  }

  if (exists) {
    // Profile already exists for user
    if (redirectSlug) {
      redirect(`/${redirectSlug}`);
    } else {
      redirect("/dashboard/customer");
    }
  }

  // Create the customer profile
  const { error: insertError } = await createUserProfile(supabase, {
    [COLUMNS.ID]: userId,
    [COLUMNS.NAME]: user.user_metadata?.full_name ?? user.user_metadata?.name ?? null,
    [COLUMNS.EMAIL]: user.email ?? null,
  });

  if (insertError) {
    console.error("Error creating customer profile:", insertError);
    return { error: "Failed to create profile." };
  }

  // Revalidate relevant paths
  revalidatePath("/choose-role");
  revalidatePath("/dashboard/customer");

  // Redirect to the appropriate page
  if (redirectSlug) {
    redirect(`/${redirectSlug}`);
  } else {
    redirect("/dashboard/customer");
  }
}
