import { getVisitAnalytics } from "./actions";
import {
  getAuthenticatedUser,
} from "@/lib/supabase/services/sharedService";
import {
  getBusinessProfileWithInteractionMetrics,
  getLatestSubscription,
} from "@/lib/supabase/services/businessService";
import { createClient } from "@/utils/supabase/server";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertTriangle } from "lucide-react";
import { Metadata } from "next";
import { redirect } from "next/navigation";
import EnhancedAnalyticsPageClient from "./components/EnhancedAnalyticsPageClient";

export const metadata: Metadata = {
  title: "Analytics - Dukancard Business",
  robots: "noindex, nofollow",
};

export default async function AnalyticsPage() {
  const supabase = await createClient();
  const { user, error: userError } = await getAuthenticatedUser(supabase);

  if (userError || !user) {
    return redirect("/login?message=Authentication required");
  }

  // Fetch the business profile including interaction metrics
  const { data: profile, error: profileError } =
    await getBusinessProfileWithInteractionMetrics(supabase, user.id);

  if (profileError || !profile) {
    return redirect("/login?message=Profile fetch error");
  }

  // Fetch subscription data to get the plan_id
  const { data: subscription, error: _subscriptionError } =
    await getLatestSubscription(supabase, user.id);

  const planId = subscription?.plan_id || "free";

  const { data: analyticsData, error } = await getVisitAnalytics(planId);

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>Error Fetching Analytics</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  if (!analyticsData) {
    // Should ideally not happen if no error, but good practice
    return <p>No analytics data available.</p>;
  }

  return (
    <EnhancedAnalyticsPageClient
      analyticsData={analyticsData}
      userId={user.id}
      userPlan={planId}
      initialProfile={{
        total_likes: profile?.total_likes || 0,
        total_subscriptions: profile?.total_subscriptions || 0,
        average_rating: profile?.average_rating || 0,
        total_visits: profile?.total_visits || 0,
        today_visits: profile?.today_visits || 0,
        yesterday_visits: profile?.yesterday_visits || 0,
        visits_7_days: profile?.visits_7_days || 0,
        visits_30_days: profile?.visits_30_days || 0,
      }}
    />
  );
}
