"use server";

import { uploadFileToStorage, getPublicUrlFromStorage, removeFileFromStorage } from "@/lib/supabase/services/sharedService";
import { getBusinessProfileCustomAds, updateBusinessProfile } from "@/lib/supabase/services/businessService";
import { getAuthenticatedUser } from "@/lib/supabase/services/sharedService";
import { createClient } from "@/utils/supabase/server";

import { getCustomAdImagePath } from "@/lib/utils/storage-paths";
import { BUCKETS } from "@/lib/supabase/constants";
import { Tables } from "@/types/supabase";

export interface CustomAdUploadResult {
  success: boolean;
  url?: string;
  error?: string;
}

export interface CustomAdUpdateResult {
  success: boolean;
  error?: string;
}

/**
 * Upload custom ad image with compression and auto-save to database
 */
export async function uploadCustomAdImage(
  formData: FormData
): Promise<CustomAdUploadResult> {
  const supabase = await createClient();
  try {
    const { user, error: authError } = await getAuthenticatedUser(supabase);

    if (authError || !user) {
      return {
        success: false,
        error: "Authentication required",
      };
    }

    const imageFile = formData.get("image") as File;
    if (!imageFile) {
      return {
        success: false,
        error: "No image file provided",
      };
    }

    if (!imageFile.type.startsWith("image/")) {
      return {
        success: false,
        error: "Invalid file type. Please upload an image.",
      };
    }

    if (imageFile.size > 15 * 1024 * 1024) {
      return {
        success: false,
        error: "File too large. Maximum size is 15MB.",
      };
    }

    const timestamp = Date.now() + Math.floor(Math.random() * 1000);
    const imagePath = getCustomAdImagePath(user.id, timestamp);

    const fileBuffer = Buffer.from(await imageFile.arrayBuffer());

    const { success: uploadSuccess, error: uploadError } = await uploadFileToStorage(
      supabase,
      BUCKETS.BUSINESS,
      imagePath,
      fileBuffer,
      imageFile.type
    );

    if (!uploadSuccess) {
      console.error("Custom Ad Upload Error:", uploadError);
      return {
        success: false,
        error: `Failed to upload image: ${uploadError}`,
      };
    }

    const { publicUrl, error: urlError } = await getPublicUrlFromStorage(
      supabase,
      BUCKETS.BUSINESS,
      imagePath
    );

    if (!publicUrl) {
      return {
        success: false,
        error: urlError || "Could not retrieve public URL after upload.",
      };
    }

    const { error: updateError } = await updateBusinessProfile(supabase, user.id, {
      custom_ads: {
        enabled: true,
        image_url: publicUrl,
        link_url: "",
        uploaded_at: new Date().toISOString(),
      } as Tables<'business_profiles'>['custom_ads']
    });

    if (updateError) {
      console.error("Database update error:", updateError);
    }

    return {
      success: true,
      url: publicUrl,
    };

  } catch (error) {
    console.error("Custom ad upload error:", error);
    return {
      success: false,
      error: "An unexpected error occurred during upload.",
    };
  }
}

/**
 * Update custom ad link URL
 */
export async function updateCustomAdLink(linkUrl: string): Promise<CustomAdUpdateResult> {
  const supabase = await createClient();
  try {
    const { user, error: authError } = await getAuthenticatedUser(supabase);

    if (authError || !user) {
      return {
        success: false,
        error: "Authentication required",
      };
    }

    if (linkUrl && linkUrl.trim()) {
      try {
        new URL(linkUrl);
      } catch {
        return {
          success: false,
          error: "Invalid URL format",
        };
      }
    }

    const { data: customAds, error: fetchError } = await getBusinessProfileCustomAds(supabase, user.id);

    if (fetchError) {
      return {
        success: false,
        error: "Failed to fetch current ad data",
      };
    }

    const updatedCustomAds = {
      ...(customAds && typeof customAds === 'object' ? customAds as Record<string, unknown> : {}),
      link_url: linkUrl.trim(),
    };

    const { error: updateError } = await updateBusinessProfile(supabase, user.id, { custom_ads: updatedCustomAds as Tables<'business_profiles'>['custom_ads'] });

    if (updateError) {
      return {
        success: false,
        error: "Failed to update ad link",
      };
    }

    return { success: true };

  } catch (error) {
    console.error("Custom ad link update error:", error);
    return {
      success: false,
      error: "An unexpected error occurred",
    };
  }
}

/**
 * Toggle custom ad enabled/disabled state
 */
export async function toggleCustomAd(enabled: boolean): Promise<CustomAdUpdateResult> {
  const supabase = await createClient();
  try {
    const { user, error: authError } = await getAuthenticatedUser(supabase);

    if (authError || !user) {
      return {
        success: false,
        error: "Authentication required",
      };
    }

    const { data: customAds, error: fetchError } = await getBusinessProfileCustomAds(supabase, user.id);

    if (fetchError) {
      return {
        success: false,
        error: "Failed to fetch current ad data",
      };
    }

    const updatedCustomAds = {
      ...(customAds && typeof customAds === 'object' ? customAds as Record<string, unknown> : {}),
      enabled,
    };

    const { error: updateError } = await updateBusinessProfile(supabase, user.id, { custom_ads: updatedCustomAds as Tables<'business_profiles'>['custom_ads'] });

    if (updateError) {
      return {
        success: false,
        error: "Failed to toggle ad state",
      };
    }

    return { success: true };

  } catch (error) {
    console.error("Custom ad toggle error:", error);
    return {
      success: false,
      error: "An unexpected error occurred",
    };
  }
}

/**
 * Delete custom ad image and reset data
 */
export async function deleteCustomAd(): Promise<CustomAdUpdateResult> {
  const supabase = await createClient();
  try {
    const { user, error: authError } = await getAuthenticatedUser(supabase);

    if (authError || !user) {
      return {
        success: false,
        error: "Authentication required",
      };
    }

    const { data: customAds, error: fetchError } = await getBusinessProfileCustomAds(supabase, user.id);

    if (fetchError) {
      return {
        success: false,
        error: "Failed to fetch current ad data",
      };
    }

    const imageUrl = customAds && typeof customAds === 'object' && 'image_url' in customAds ? (customAds as Record<string, unknown>).image_url : null;

    if (imageUrl && typeof imageUrl === 'string') {
      try {
        const urlParts = imageUrl.split('/storage/v1/object/public/' + BUCKETS.BUSINESS + '/');
        if (urlParts.length === 2) {
          const filePath = urlParts[1];

          const { success: deleteSuccess, error: deleteError } = await removeFileFromStorage(
            supabase,
            BUCKETS.BUSINESS,
            [filePath]
          );

          if (!deleteSuccess) {
            console.error("Storage deletion error:", deleteError);
          }
        }
      } catch (storageError) {
        console.error("Error deleting custom ad from storage:", storageError);
      }
    }

    const { error: updateError } = await updateBusinessProfile(supabase, user.id, {
      custom_ads: {
        enabled: false,
        image_url: "",
        link_url: "",
        uploaded_at: null,
      } as Tables<'business_profiles'>['custom_ads']
    });

    if (updateError) {
      return {
        success: false,
        error: "Failed to delete custom ad",
      };
    }

    return { success: true };

  } catch (error) {
    console.error("Custom ad delete error:", error);
    return {
      success: false,
      error: "An unexpected error occurred",
    };
  }
}
