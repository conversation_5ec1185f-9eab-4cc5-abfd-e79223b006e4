"use server";

import { uploadFileToStorage, getPublicUrlFromStorage, removeFileFromStorage, getAuthenticatedUser } from "@/lib/supabase/services/sharedService";
import { getBusinessProfileCustomBranding, updateBusinessProfile } from "@/lib/supabase/services/businessService";
import { createClient } from "@/utils/supabase/server";
import { Tables } from "@/types/supabase";

import { getCustomHeaderImagePath } from "@/lib/utils/storage-paths";
import { BUCKETS } from "@/lib/supabase/constants";

export interface CustomHeaderUploadResult {
  success: boolean;
  url?: string;
  error?: string;
}

export interface CustomHeaderUpdateResult {
  success: boolean;
  error?: string;
}

/**
 * Upload custom header image with compression and auto-save to database
 */
export async function uploadCustomHeaderImage(
  formData: FormData
): Promise<CustomHeaderUploadResult> {
  const supabase = await createClient();
  try {
    const { user, error: authError } = await getAuthenticatedUser(supabase);

    if (authError || !user) {
      return {
        success: false,
        error: "Authentication required",
      };
    }

    const imageFile = formData.get("image") as File;
    if (!imageFile) {
      return {
        success: false,
        error: "No image file provided",
      };
    }

    if (!imageFile.type.startsWith("image/")) {
      return {
        success: false,
        error: "Please upload a valid image file",
      };
    }

    const maxSizeBytes = 5 * 1024 * 1024; // 5MB
    if (imageFile.size > maxSizeBytes) {
      const fileSizeMB = (imageFile.size / (1024 * 1024)).toFixed(1);
      return {
        success: false,
        error: `Image size (${fileSizeMB}MB) is too large. Please choose an image smaller than 5MB for optimal performance and faster loading.`,
      };
    }

    const timestamp = Date.now() + Math.floor(Math.random() * 1000);
    const imagePath = getCustomHeaderImagePath(user.id, timestamp);

    const fileBuffer = Buffer.from(await imageFile.arrayBuffer());

    const { success: uploadSuccess, error: uploadError } = await uploadFileToStorage(
      supabase,
      BUCKETS.BUSINESS,
      imagePath,
      fileBuffer,
      imageFile.type
    );

    if (!uploadSuccess) {
      console.error("Custom Header Upload Error:", uploadError);
      return {
        success: false,
        error: `Failed to upload image: ${uploadError}`,
      };
    }

    const { publicUrl, error: urlError } = await getPublicUrlFromStorage(
      supabase,
      BUCKETS.BUSINESS,
      imagePath
    );

    if (!publicUrl) {
      return {
        success: false,
        error: urlError || "Could not retrieve public URL after upload.",
      };
    }

    const { data: currentBranding, error: fetchError } = await getBusinessProfileCustomBranding(supabase, user.id);

    if (fetchError) {
      return {
        success: false,
        error: "Failed to fetch current branding data",
      };
    }

    const updatedCustomBranding = {
      ...(typeof currentBranding === 'object' && currentBranding !== null ? currentBranding : {}),
      custom_header_image_url: publicUrl,
      hide_dukancard_branding: true,
    };

    const { error: updateError } = await updateBusinessProfile(supabase, user.id, {
      custom_branding: updatedCustomBranding as Tables<'business_profiles'>['custom_branding']
    });

    if (updateError) {
      console.error("Database update error:", updateError);
    }

    return {
      success: true,
      url: publicUrl,
    };

  } catch (error) {
    console.error("Custom header upload error:", error);
    return {
      success: false,
      error: "An unexpected error occurred during upload.",
    };
  }
}

/**
 * Delete custom header image and reset data
 */
export async function deleteCustomHeaderImage(): Promise<CustomHeaderUpdateResult> {
  const supabase = await createClient();
  try {
    const { user, error: authError } = await getAuthenticatedUser(supabase);

    if (authError || !user) {
      return {
        success: false,
        error: "Authentication required",
      };
    }

    const { data: currentBranding, error: fetchError } = await getBusinessProfileCustomBranding(supabase, user.id);

    if (fetchError) {
      return {
        success: false,
        error: "Failed to fetch current branding data",
      };
    }

    const imageUrl = (typeof currentBranding === 'object' && currentBranding !== null && 'custom_header_image_url' in currentBranding) ? currentBranding.custom_header_image_url as string : null;

    if (imageUrl) {
      try {
        const urlParts = imageUrl.split('/storage/v1/object/public/' + BUCKETS.BUSINESS + '/');
        if (urlParts.length === 2) {
          const filePath = urlParts[1];

          const { success: deleteSuccess, error: deleteError } = await removeFileFromStorage(
            supabase,
            BUCKETS.BUSINESS,
            [filePath]
          );

          if (!deleteSuccess) {
            console.error("Storage deletion error:", deleteError);
          }
        }
      } catch (storageError) {
        console.error("Error deleting custom header from storage:", storageError);
      }
    }

    const updatedCustomBranding = {
      ...(typeof currentBranding === 'object' && currentBranding !== null ? currentBranding : {}),
      custom_header_image_url: "",
    };

    const { error: updateError } = await updateBusinessProfile(supabase, user.id, {
      custom_branding: updatedCustomBranding as Tables<'business_profiles'>['custom_branding']
    });

    if (updateError) {
      return {
        success: false,
        error: "Failed to delete custom header image",
      };
    }

    return { success: true };

  } catch (error) {
    console.error("Custom header delete error:", error);
    return {
      success: false,
      error: "An unexpected error occurred",
    };
  }
}