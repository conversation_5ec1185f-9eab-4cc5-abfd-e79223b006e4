"use server";

import { uploadFileToStorage, getPublicUrlFromStorage, removeFileFromStorage, getAuthenticatedUser, listStorageFiles } from "@/lib/supabase/services/sharedService";
import { createClient } from "@/utils/supabase/server";

import { getThemeSpecificHeaderImagePath } from "@/lib/utils/storage-paths";
import { BUCKETS } from "@/lib/supabase/constants";

export interface ThemeHeaderUploadResult {
  success: boolean;
  url?: string;
  error?: string;
}

export interface ThemeHeaderDeleteResult {
  success: boolean;
  error?: string;
}

/**
 * Upload theme-specific custom header image with compression
 * This function only uploads to storage and returns the URL
 * Database update happens in the main save action
 */
export async function uploadThemeHeaderImage(
  imageFile: File,
  theme: 'light' | 'dark'
): Promise<ThemeHeaderUploadResult> {
  const supabase = await createClient();
  try {
    const { user, error: authError } = await getAuthenticatedUser(supabase);

    if (authError || !user) {
      return {
        success: false,
        error: "Authentication required",
      };
    }

    if (!imageFile.type.startsWith("image/")) {
      return {
        success: false,
        error: "Invalid file type. Please upload an image file.",
      };
    }

    const maxSizeBytes = 5 * 1024 * 1024; // 5MB
    if (imageFile.size > maxSizeBytes) {
      const fileSizeMB = (imageFile.size / (1024 * 1024)).toFixed(1);
      return {
        success: false,
        error: `Image size (${fileSizeMB}MB) is too large. Please choose an image smaller than 5MB.`,
      };
    }

    const timestamp = Date.now() + Math.floor(Math.random() * 1000);
    const imagePath = getThemeSpecificHeaderImagePath(user.id, timestamp, theme);

    const fileBuffer = Buffer.from(await imageFile.arrayBuffer());

    const { success: uploadSuccess, error: uploadError } = await uploadFileToStorage(
      supabase,
      BUCKETS.BUSINESS,
      imagePath,
      fileBuffer,
      imageFile.type
    );

    if (!uploadSuccess) {
      console.error("Theme Header Upload Error:", uploadError);
      return {
        success: false,
        error: `Failed to upload image: ${uploadError}`,
      };
    }

    const { publicUrl, error: urlError } = await getPublicUrlFromStorage(
      supabase,
      BUCKETS.BUSINESS,
      imagePath
    );

    if (!publicUrl) {
      return {
        success: false,
        error: urlError || "Could not retrieve public URL after upload.",
      };
    }

    return {
      success: true,
      url: publicUrl,
    };

  } catch (error) {
    console.error("Theme header upload error:", error);
    return {
      success: false,
      error: "An unexpected error occurred during upload.",
    };
  }
}

/**
 * Delete theme-specific custom header image from storage
 * This function only deletes from storage
 * Database update happens in the main save action
 */
export async function deleteThemeHeaderImage(
  imageUrl: string
): Promise<ThemeHeaderDeleteResult> {
  const supabase = await createClient();
  try {
    const { user, error: authError } = await getAuthenticatedUser(supabase);

    if (authError || !user) {
      return {
        success: false,
        error: "Authentication required",
      };
    }

    if (!imageUrl || imageUrl.trim() === "") {
      return {
        success: true, // Nothing to delete
      };
    }

    try {
      const url = new URL(imageUrl);
      const pathParts = url.pathname.split('/');

      const businessIndex = pathParts.findIndex(part => part === BUCKETS.BUSINESS);

      if (businessIndex !== -1 && businessIndex < pathParts.length - 1) {
        const storagePath = pathParts.slice(businessIndex + 1).join('/').split('?')[0];

        const { success: deleteSuccess, error: deleteError } = await removeFileFromStorage(
          supabase,
          BUCKETS.BUSINESS,
          [storagePath]
        );

        if (!deleteSuccess && deleteError !== "The resource was not found") {
          console.error("Error deleting theme header from storage:", deleteError);
          return {
            success: false,
            error: `Failed to delete image: ${deleteError}`,
          };
        }
      }
    } catch (urlError) {
      console.error("Error processing image URL for deletion:", urlError);
      return {
        success: false,
        error: "Invalid image URL format.",
      };
    }

    return {
      success: true,
    };

  } catch (error) {
    console.error("Theme header deletion error:", error);
    return {
      success: false,
      error: "An unexpected error occurred during deletion.",
    };
  }
}

/**
 * Clean up old theme-specific header images for a user
 * This is used to remove old images when new ones are uploaded
 */
export async function cleanupOldThemeHeaderImages(
  userId: string,
  theme: 'light' | 'dark',
  keepUrl?: string
): Promise<void> {
  const supabase = await createClient();
  try {
    const userPath = userId.slice(0, 2) + '/' + userId.slice(2, 4) + '/' + userId;
    const brandingFolderPath = `${userPath}/branding/`;

    const { data: existingFiles, error: listError } = await listStorageFiles(
      supabase,
      BUCKETS.BUSINESS,
      brandingFolderPath,
      { limit: 20 }
    );

    if (listError || !existingFiles) {
      console.error("Error listing branding files:", listError);
      return;
    }

    const themeHeaderFiles = existingFiles.filter((file: { name: string; }) => 
      file.name.startsWith(`header_${theme}_`) && file.name.endsWith('.webp')
    );

    let keepFilename: string | undefined;
    if (keepUrl) {
      try {
        const url = new URL(keepUrl);
        const pathParts = url.pathname.split('/');
        keepFilename = pathParts[pathParts.length - 1].split('?')[0];
      } catch (error) {
        console.error("Error extracting filename from keep URL:", error);
      }
    }

    const filesToDelete = themeHeaderFiles
      .filter((file: { name: string; }) => !keepFilename || file.name !== keepFilename)
      .map((file: { name: string; }) => `${brandingFolderPath}${file.name}`);

    if (filesToDelete.length > 0) {
      const { success: deleteSuccess, error: deleteError } = await removeFileFromStorage(
        supabase,
        BUCKETS.BUSINESS,
        filesToDelete
      );

      if (!deleteSuccess) {
        console.error("Error cleaning up old theme header files:", deleteError);
      }
    }
  } catch (error) {
    console.error("Error in cleanup function:", error);
  }
}