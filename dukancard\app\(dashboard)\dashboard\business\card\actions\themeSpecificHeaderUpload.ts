"use server";

import { uploadFileToStorage, getPublicUrlFromStorage, removeFileFromStorage, getAuthenticatedUser } from "@/lib/supabase/services/sharedService";
import { getBusinessProfileCustomBranding, updateBusinessProfile } from "@/lib/supabase/services/businessService";
import { createClient } from "@/utils/supabase/server";
import { Tables } from "@/types/supabase";

import { getThemeSpecificHeaderImagePath } from "@/lib/utils/storage-paths";
import { BUCKETS } from "@/lib/supabase/constants";

export interface ThemeSpecificHeaderUploadResult {
  success: boolean;
  url?: string;
  error?: string;
}

export interface ThemeSpecificHeaderUpdateResult {
  success: boolean;
  error?: string;
}

/**
 * Upload theme-specific custom header image with compression and auto-save to database
 */
export async function uploadThemeSpecificHeaderImage(
  formData: FormData,
  theme: 'light' | 'dark'
): Promise<ThemeSpecificHeaderUploadResult> {
  const supabase = await createClient();
  try {
    const { user, error: authError } = await getAuthenticatedUser(supabase);

    if (authError || !user) {
      return {
        success: false,
        error: "Authentication required",
      };
    }

    const imageFile = formData.get("image") as File;
    if (!imageFile) {
      return {
        success: false,
        error: "No image file provided",
      };
    }

    const allowedTypes = ["image/jpeg", "image/jpg", "image/png", "image/webp"];
    if (!allowedTypes.includes(imageFile.type)) {
      return {
        success: false,
        error: "Invalid file type. Please upload a JPEG, PNG, or WebP image.",
      };
    }

    const maxSizeBytes = 5 * 1024 * 1024; // 5MB
    if (imageFile.size > maxSizeBytes) {
      const fileSizeMB = (imageFile.size / (1024 * 1024)).toFixed(1);
      return {
        success: false,
        error: `Image size (${fileSizeMB}MB) is too large. Please choose an image smaller than 5MB for optimal performance and faster loading.`,
      };
    }

    const timestamp = Date.now() + Math.floor(Math.random() * 1000);
    const imagePath = getThemeSpecificHeaderImagePath(user.id, timestamp, theme);

    const fileBuffer = Buffer.from(await imageFile.arrayBuffer());

    const { success: uploadSuccess, error: uploadError } = await uploadFileToStorage(
      supabase,
      BUCKETS.BUSINESS,
      imagePath,
      fileBuffer,
      imageFile.type
    );

    if (!uploadSuccess) {
      console.error("Theme-Specific Header Upload Error:", uploadError);
      return {
        success: false,
        error: `Failed to upload image: ${uploadError}`,
      };
    }

    const { publicUrl, error: urlError } = await getPublicUrlFromStorage(
      supabase,
      BUCKETS.BUSINESS,
      imagePath
    );

    if (!publicUrl) {
      return {
        success: false,
        error: urlError || "Could not retrieve public URL after upload.",
      };
    }

    const { data: currentBranding, error: fetchError } = await getBusinessProfileCustomBranding(supabase, user.id);

    if (fetchError) {
      return {
        success: false,
        error: "Failed to fetch current branding data",
      };
    }

    const fieldName = theme === 'light' ? 'custom_header_image_light_url' : 'custom_header_image_dark_url';

    const updatedBranding = {
      ...(typeof currentBranding === 'object' && currentBranding !== null ? currentBranding : {}),
      [fieldName]: publicUrl,
      hide_dukancard_branding: true,
    };

    const { error: updateError } = await updateBusinessProfile(supabase, user.id, {
      custom_branding: updatedBranding as Tables<'business_profiles'>['custom_branding']
    });

    if (updateError) {
      console.error("Database update error:", updateError);
      return {
        success: false,
        error: "Failed to save branding data to database",
      };
    }

    return {
      success: true,
      url: publicUrl,
    };

  } catch (error) {
    console.error("Theme-specific header upload error:", error);
    return {
      success: false,
      error: "An unexpected error occurred during upload.",
    };
  }
}

/**
 * Delete theme-specific custom header image and reset data
 */
export async function deleteThemeSpecificHeaderImage(
  theme: 'light' | 'dark'
): Promise<ThemeSpecificHeaderUpdateResult> {
  const supabase = await createClient();
  try {
    const { user, error: authError } = await getAuthenticatedUser(supabase);

    if (authError || !user) {
      return {
        success: false,
        error: "Authentication required",
      };
    }

    const { data: currentBranding, error: fetchError } = await getBusinessProfileCustomBranding(supabase, user.id);

    if (fetchError) {
      return {
        success: false,
        error: "Failed to fetch current branding data",
      };
    }

    const fieldName = theme === 'light' ? 'custom_header_image_light_url' : 'custom_header_image_dark_url';
    const imageUrl = (typeof currentBranding === 'object' && currentBranding !== null && 'custom_header_image_url' in currentBranding) ? currentBranding.custom_header_image_url as string : null;

    if (imageUrl) {
      try {
        const urlParts = imageUrl.split('/');
        const _fileName = urlParts[urlParts.length - 1];
        const userIdFromUrl = urlParts[urlParts.length - 4];

        if (userIdFromUrl === user.id) {
          const pathParts = urlParts.slice(-4);
          const filePath = pathParts.join('/');

          const { success: deleteSuccess, error: deleteError } = await removeFileFromStorage(
            supabase,
            BUCKETS.BUSINESS,
            [filePath]
          );

          if (!deleteSuccess) {
            console.error("Storage deletion error:", deleteError);
          }
        }
      } catch (storageError) {
        console.error("Storage deletion error:", storageError);
      }
    }

    const updatedBranding: Record<string, unknown> = {
      ...(typeof currentBranding === 'object' && currentBranding !== null ? currentBranding : {}),
      [fieldName]: "",
    };

    const hasAnyImage = !!(
      ((updatedBranding as Record<string, unknown>).custom_header_image_url as string)?.trim() ||
      ((updatedBranding as Record<string, unknown>).custom_header_image_light_url as string)?.trim() ||
      ((updatedBranding as Record<string, unknown>).custom_header_image_dark_url as string)?.trim()
    );
    const hasText = !!(((updatedBranding as Record<string, unknown>).custom_header_text as string)?.trim());

    if (!hasAnyImage && !hasText) {
      updatedBranding.hide_dukancard_branding = false;
    }

    const { error: updateError } = await updateBusinessProfile(supabase, user.id, {
      custom_branding: updatedBranding as Tables<'business_profiles'>['custom_branding']
    });

    if (updateError) {
      console.error("Database update error:", updateError);
      return {
        success: false,
        error: "Failed to update branding data in database",
      };
    }

    return {
      success: true,
    };

  } catch (error) {
    console.error("Theme-specific header deletion error:", error);
    return {
      success: false,
      error: "An unexpected error occurred during deletion.",
    };
  }
}