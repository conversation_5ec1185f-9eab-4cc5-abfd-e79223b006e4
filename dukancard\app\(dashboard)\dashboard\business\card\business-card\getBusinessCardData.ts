"use server";

import { getAuthenticatedUser, getBusinessProfileWithAllDetails, updateBusinessProfile } from "@/lib/supabase/services/userService";
import { BusinessCardData, requiredFieldsForOnline } from "../schema";
import { mapBusinessCardData } from "../data/businessCardMapper";
import { checkForceOfflineStatus } from "../data/subscriptionChecker";
import { createClient } from "@/utils/supabase/server";

/**
 * Fetches business card data for the authenticated user
 * @returns Business card data or error
 */
export async function getBusinessCardData(): Promise<{
  data?: BusinessCardData;
  error?: string;
}> {
  const supabase = await createClient();
  const { user, error: authError } = await getAuthenticatedUser(supabase);

  if (authError || !user) {
    return { error: "User not authenticated." };
  }

  const { data, error } = await getBusinessProfileWithAllDetails(supabase, user.id);

  if (error) {
    if (error === "PGRST116") {
      return { data: undefined };
    }
    console.error("Supabase Fetch Error:", error);
    return { error: `Failed to fetch profile: ${error}` };
  }

  if (!data) {
    return { error: "No business profile found." };
  }

  if (data.status === "online") {
    let shouldForceOffline = false;
    let reason = "";

    const subscriptionCheck = await checkForceOfflineStatus(user.id);
    if (subscriptionCheck.shouldForceOffline) {
      shouldForceOffline = true;
      reason = subscriptionCheck.reason || "subscription issue";
    }

    const missingRequiredFields = requiredFieldsForOnline.filter(
      (field) => {
        const value = (data as Record<string, unknown>)[field];
        return !value || String(value).trim() === "";
      }
    );

    if (missingRequiredFields.length > 0) {
      shouldForceOffline = true;
      reason = `missing required fields: ${missingRequiredFields.join(", ")}`;
    }

    if (shouldForceOffline) {
      console.log(
        `User ${user.id} card forced offline due to ${reason}.`
      );
      const { error: updateError } = await updateBusinessProfile(supabase, user.id, { status: "offline" });

      if (updateError) {
        console.error("Error forcing card offline:", updateError);
      } else {
        data.status = "offline";
      }
    }
  }

  const mappedData = mapBusinessCardData(data as any);
  return { data: mappedData };
}