"use server";

import { getA<PERSON><PERSON><PERSON>ted<PERSON>ser, getBusinessProfilePhone, updateAuthUserPhone, updateBusinessProfile, getBusinessProfileCustomBranding } from "@/lib/supabase/services/userService";
import { revalidatePath } from "next/cache";
import { BusinessCardData, requiredFieldsForOnline } from "../schema";
import { validateBusinessCardData } from "../validation/businessCardValidation";
import { checkSubscriptionStatus } from "../data/subscriptionChecker";
import { generateUniqueSlug } from "../slug/slugUtils";
import { processBusinessHours } from "../utils/businessHoursProcessor";
import { uploadThemeHeaderImage, deleteThemeHeaderImage, cleanupOldThemeHeaderImages } from "../actions/themeHeaderActions";
import { createClient } from "@/utils/supabase/server";

/**
 * Updates business card data with validation and processing
 * @param formData - The business card data to update
 * @returns Success/error response with updated data
 */
export async function updateBusinessCard(
  formData: BusinessCardData
): Promise<{ success: boolean; error?: string; data?: BusinessCardData }> {
  const validatedFields = validateBusinessCardData(formData);

  if (!validatedFields.success) {
    console.error(
      "Validation Error:",
      validatedFields.error.flatten().fieldErrors
    );
    return {
      success: false,
      error: "Invalid data provided. Please check the form fields.",
    };
  }

  const supabase = await createClient();
  const { user, error: authError } = await getAuthenticatedUser(supabase);

  if (authError || !user) {
    console.error("Auth Error:", authError);
    return { success: false, error: "User not authenticated." };
  }

  const { phone: existingPhone, error: profileError } = await getBusinessProfilePhone(supabase, user.id);

  if (profileError) {
    console.error("Profile fetch error:", profileError);
    return { success: false, error: "Failed to fetch existing profile." };
  }

  if (validatedFields.data.status === "online") {
    const subscriptionCheck = await checkSubscriptionStatus(user.id);
    if (!subscriptionCheck.canGoOnline) {
      return {
        success: false,
        error: subscriptionCheck.error || "Cannot set card to online status."
      };
    }
  }

  let finalSlug = validatedFields.data.business_slug;

  if (validatedFields.data.status === "online") {
    const slugResult = await generateUniqueSlug(
      validatedFields.data.business_name,
      finalSlug || "",
      user.id
    );

    if (!slugResult.success) {
      return {
        success: false,
        error: slugResult.error || "Failed to generate unique slug."
      };
    }

    finalSlug = slugResult.slug;
  } else {
    finalSlug = validatedFields.data.business_slug;
  }

  const updatedCustomBranding = { ...formData.custom_branding };

  if (formData.custom_branding?.pending_light_header_file) {
    const lightFile = formData.custom_branding.pending_light_header_file as File;
    const lightUploadResult = await uploadThemeHeaderImage(lightFile, 'light');

    if (lightUploadResult.success && lightUploadResult.url) {
      if (updatedCustomBranding.custom_header_image_light_url) {
        await deleteThemeHeaderImage(updatedCustomBranding.custom_header_image_light_url);
      }
      await cleanupOldThemeHeaderImages(user.id, 'light', lightUploadResult.url);

      updatedCustomBranding.custom_header_image_light_url = lightUploadResult.url;
    } else {
      console.error("Light theme header upload failed:", lightUploadResult.error);
      return {
        success: false,
        error: `Failed to upload light theme header: ${lightUploadResult.error}`,
      };
    }
  }

  if (formData.custom_branding?.pending_dark_header_file) {
    const darkFile = formData.custom_branding.pending_dark_header_file as File;
    const darkUploadResult = await uploadThemeHeaderImage(darkFile, 'dark');

    if (darkUploadResult.success && darkUploadResult.url) {
      if (updatedCustomBranding.custom_header_image_dark_url) {
        await deleteThemeHeaderImage(updatedCustomBranding.custom_header_image_dark_url);
      }
      await cleanupOldThemeHeaderImages(user.id, 'dark', darkUploadResult.url);

      updatedCustomBranding.custom_header_image_dark_url = darkUploadResult.url;
    } else {
      console.error("Dark theme header upload failed:", darkUploadResult.error);
      return {
        success: false,
        error: `Failed to upload dark theme header: ${darkUploadResult.error}`,
      };
    }
  }

  if (formData.custom_branding?.custom_header_image_light_url === "" &&
      !formData.custom_branding?.pending_light_header_file) {
    const { data: currentProfileBranding } = await getBusinessProfileCustomBranding(supabase, user.id);

    if (currentProfileBranding && typeof currentProfileBranding === 'object' && 'custom_header_image_light_url' in currentProfileBranding && currentProfileBranding.custom_header_image_light_url) {
      await deleteThemeHeaderImage(currentProfileBranding.custom_header_image_light_url as string);
    }
    updatedCustomBranding.custom_header_image_light_url = "";
  }

  if (formData.custom_branding?.custom_header_image_dark_url === "" &&
      !formData.custom_branding?.pending_dark_header_file) {
    const { data: currentProfileBranding } = await getBusinessProfileCustomBranding(supabase, user.id);

    if (currentProfileBranding && typeof currentProfileBranding === 'object' && 'custom_header_image_dark_url' in currentProfileBranding && currentProfileBranding.custom_header_image_dark_url) {
      await deleteThemeHeaderImage(currentProfileBranding.custom_header_image_dark_url as string);
    }
    updatedCustomBranding.custom_header_image_dark_url = "";
  }

  delete updatedCustomBranding.pending_light_header_file;
  delete updatedCustomBranding.pending_dark_header_file;

  const businessHoursData = processBusinessHours(validatedFields.data.business_hours);

  const dataToUpdate: Partial<BusinessCardData> = {
    business_name: validatedFields.data.business_name,
    member_name: validatedFields.data.member_name,
    title: validatedFields.data.title,
    logo_url: validatedFields.data.logo_url,
    established_year: validatedFields.data.established_year,
    address_line: validatedFields.data.address_line,
    city: validatedFields.data.city,
    state: validatedFields.data.state,
    pincode: validatedFields.data.pincode,
    phone: validatedFields.data.phone,
    delivery_info: validatedFields.data.delivery_info,
    
    instagram_url: validatedFields.data.instagram_url,
    facebook_url: validatedFields.data.facebook_url,
    whatsapp_number: validatedFields.data.whatsapp_number,
    about_bio: validatedFields.data.about_bio,
    locality: validatedFields.data.locality,
    theme_color: validatedFields.data.theme_color,
    business_hours: businessHoursData,
    status: validatedFields.data.status,
    business_slug: finalSlug,
    contact_email: validatedFields.data.contact_email,
    business_category: validatedFields.data.business_category,
    custom_branding: updatedCustomBranding,
    custom_ads: validatedFields.data.custom_ads,
  };

  const { data: updatedProfile, error: updateError } = await updateBusinessProfile(supabase, user.id, dataToUpdate);

  if (updateError) {
    console.error("Supabase Update Error:", updateError);
    return {
      success: false,
      error: `Failed to update profile: ${updateError}`,
    };
  }

  if (!updatedProfile) {
    return {
      success: false,
      error: "Failed to update profile. Profile not found after update.",
    };
  }

  

  revalidatePath("/dashboard/business/card");
  if (dataToUpdate.status === "online" && dataToUpdate.business_slug) {
    revalidatePath(`/(main)/card/${dataToUpdate.business_slug}`, "page");
  }

  return { success: true, data: updatedProfile as BusinessCardData };
}