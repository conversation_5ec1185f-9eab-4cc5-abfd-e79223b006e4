"use server";

import { getLatestSubscriptionStatus } from "@/lib/supabase/services/userService";
import { TABLES, COLUMNS } from "@/lib/supabase/constants";
import { createClient } from "@/utils/supabase/server";

/**
 * Checks if a user's subscription allows them to go online
 * @param userId - The user ID to check
 * @returns Object indicating if user can go online and any error message
 */
export async function checkSubscriptionStatus(userId: string): Promise<{
  canGoOnline: boolean;
  error?: string;
}> {
  const supabase = await createClient();
  const { subscriptionStatus, error: subscriptionError } = await getLatestSubscriptionStatus(supabase, userId);

  if (subscriptionError) {
    console.error("Error fetching subscription data:", subscriptionError);
    return { canGoOnline: true };
  }

  if (subscriptionStatus?.subscription_status === "halted") {
    console.log(`User ${userId} attempted to set card online with halted subscription`);
    return {
      canGoOnline: false,
      error: "Cannot set card to online status while your subscription is paused. Please resume your subscription first."
    };
  }

  return { canGoOnline: true };
}

/**
 * Checks subscription status for forcing offline during data fetch
 * @param userId - The user ID to check
 * @param supabase - Supabase client instance
 * @returns Object indicating if card should be forced offline
 */
export async function checkForceOfflineStatus(userId: string): Promise<{
  shouldForceOffline: boolean;
  reason?: string;
}> {
  const supabase = await createClient();
  const { subscriptionStatus, error: subscriptionError } = await getLatestSubscriptionStatus(supabase, userId);

  if (subscriptionError) {
    console.error("Error fetching subscription data:", subscriptionError);
    return { shouldForceOffline: false };
  }

  const { SUBSCRIPTION_STATUS } = await import('@/lib/razorpay/webhooks/handlers/utils');

  if (subscriptionStatus?.subscription_status === SUBSCRIPTION_STATUS.HALTED) {
    return {
      shouldForceOffline: true,
      reason: "subscription is paused"
    };
  }

  return { shouldForceOffline: false };
}