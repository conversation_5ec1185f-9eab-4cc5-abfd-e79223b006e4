"use server";

import * as userService from "@/lib/supabase/services/userService";
import { BusinessCardData } from "../schema";
import { ProductServiceData } from "../../products/actions";
import { mapPublicCardData } from "../data/businessCardMapper";
import { COLUMNS, TABLES } from "@/lib/supabase/constants";
import { createClient } from "@/utils/supabase/server";

/**
 * Fetches public card data by business slug
 * @param slug - The business slug to fetch data for
 * @returns Public card data with products/services or error
 */
export async function getPublicCardDataBySlug(slug: string): Promise<{
  data?: BusinessCardData & { products_services?: ProductServiceData[] };
  error?: string;
}> {
  if (!slug) {
    return { error: "Business slug is required." };
  }

  const supabase = await createClient();

  // Fetch business ID and status first
  const { data: businessStatusData, error: statusError } = await userService.getBusinessProfileIdAndStatusBySlug(supabase, slug);

  if (statusError || !businessStatusData) {
    console.error("Public Fetch Error: Could not retrieve business status.", statusError);
    return { error: "Profile not found or is not online." };
  }

  if (businessStatusData.status !== "online") {
    return { error: "Profile not found or is not online." };
  }

  // Fetch profile and related products using the secure function
  const { data, error } = await userService.getSecureBusinessProfileWithProductsBySlug(supabase, slug);

  if (error) {
    console.error("Public Fetch Error:", error);
    return { error: `Failed to fetch public profile: ${error}` };
  }

  if (!data) {
    return { error: "Profile not found or is not online." };
  }

  // Map data using the shared mapper
  const mappedData = mapPublicCardData(data);
  return { data: mappedData };
}