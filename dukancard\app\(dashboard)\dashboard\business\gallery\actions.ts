"use server";

import * as userService from "@/lib/supabase/services/userService";

import { revalidatePath } from "next/cache";
import { getGalleryImagePath } from "@/lib/utils/storage-paths";
import { GalleryImage } from "@/types/gallery";
import {
  UploadGalleryImageResponse,
  DeleteGalleryImageResponse,
  GetGalleryImagesResponse
} from "./types";
import { getGalleryLimit, canAddMoreGalleryImages } from "./utils";
import { BUCKETS, COLUMNS, TABLES } from "@/lib/supabase/constants";
import { <PERSON><PERSON> } from "@/types/supabase";
import { createClient } from "@/utils/supabase/server";

/**
 * Upload a gallery image
 */
export async function uploadGalleryImage(
  formData: FormData
): Promise<UploadGalleryImageResponse> {
  const supabase = await createClient();
  const { user, error: authError } = await userService.getAuthenticatedUser(supabase);

  if (authError || !user) {
    return { success: false, error: "User not authenticated." };
  }

  const userId = user.id;

  // Get the file from the form data
  const file = formData.get("image") as File | null;
  if (!file) {
    return { success: false, error: "No image file provided." };
  }

  // Check file type
  const allowedTypes = ["image/png", "image/jpeg", "image/gif", "image/webp"];
  if (!allowedTypes.includes(file.type)) {
    return { success: false, error: "Invalid file type." };
  }

  // Server-side file size validation (15MB limit)
  if (file.size > 15 * 1024 * 1024) {
    return { success: false, error: "File size must be less than 15MB." };
  }

  // Get the current plan from payment_subscriptions
  const { subscriptionStatus, error: subscriptionError } = await userService.getLatestSubscriptionStatus(supabase, userId);

  if (subscriptionError) {
    console.error("Error fetching subscription data:", subscriptionError);
  }

  // Default to free plan if no subscription found
  const planId = subscriptionStatus?.plan_id || "free";

  // Get current gallery images
  const { data: profileGalleryData, error: profileError } = await userService.getBusinessProfileGallery(supabase, userId);

  if (profileError) {
    return { success: false, error: "Failed to fetch business profile." };
  }

  // Parse gallery data
  const gallery = Array.isArray(profileGalleryData) ? (profileGalleryData as unknown as GalleryImage[]) : [];
  const currentCount = Array.isArray(gallery) ? gallery.length : 0;

  // Check if user can add more images
  const canAddMore = canAddMoreGalleryImages(planId, currentCount);
  if (!canAddMore) {
    return {
      success: false,
      error: `You have reached the limit of ${getGalleryLimit(planId)} gallery images for your ${planId} plan. Please upgrade your plan to add more images.`
    };
  }

  try {
    const timestamp = new Date().getTime();
    const imagePath = getGalleryImagePath(userId, timestamp);

    // File is already compressed on client-side, just upload it
    const fileBuffer = Buffer.from(await file.arrayBuffer());

    // Upload to Supabase Storage using admin client
    const { error: uploadError } = await userService.uploadFileToStorage(
      supabase,
      BUCKETS.BUSINESS,
      imagePath,
      fileBuffer,
      file.type,
      true
    );

    if (uploadError) {
      console.error("Gallery Image Upload Error:", uploadError);
      return {
        success: false,
        error: `Failed to upload gallery image: ${uploadError}`,
      };
    }

    // Get the public URL using admin client
    const { publicUrl, error: urlError } = await userService.getPublicUrlFromStorage(supabase, BUCKETS.BUSINESS, imagePath);

    if (urlError || !publicUrl) {
      return {
        success: false,
        error: "Could not retrieve public URL after upload.",
      };
    }

    // Create new gallery image object
    const newImage: GalleryImage = {
      id: `gallery_${timestamp}`,
      url: publicUrl,
      path: imagePath,
      created_at: new Date().toISOString()
    };

    // Update the gallery array in business_profiles
    const updatedGallery = Array.isArray(gallery) ? [...gallery, newImage] : [newImage];

    const { error: updateError } = await userService.updateBusinessProfile(supabase, userId, { gallery: updatedGallery as Json });

    if (updateError) {
      console.error("Gallery Update Error:", updateError);

      // Try to clean up the uploaded image if the database update fails
      await userService.removeFileFromStorage(
        supabase,
        BUCKETS.BUSINESS,
        [imagePath]
      );

      return {
        success: false,
        error: `Failed to update gallery: ${updateError}`,
      };
    }

    revalidatePath("/dashboard/business/gallery");
    return { success: true, image: newImage };
  } catch (error) {
    console.error("Unexpected error during gallery image upload:", error);
    return {
      success: false,
      error: `An unexpected error occurred: ${error instanceof Error ? error.message : String(error)}`,
    };
  }
}

/**
 * Delete a gallery image
 */
/**
 * Update gallery images order
 */
export async function updateGalleryOrder(
  orderedImages: GalleryImage[]
): Promise<{ success: boolean; error?: string }> {
  const supabase = await createClient();
  const { user, error: authError } = await userService.getAuthenticatedUser(supabase);

  if (authError || !user) {
    return { success: false, error: "User not authenticated." };
  }

  const userId = user.id;

  try {
    // Update the gallery array in business_profiles with the new order
    const { error: updateError } = await userService.updateBusinessProfile(supabase, userId, { gallery: orderedImages as Json });

    if (updateError) {
      console.error("Gallery Order Update Error:", updateError);
      return {
        success: false,
        error: `Failed to update gallery order: ${updateError}`,
      };
    }

    // Revalidate the gallery page to reflect the new order
    revalidatePath("/dashboard/business/gallery");
    return { success: true };
  } catch (error) {
    console.error("Error updating gallery order:", error);
    return {
      success: false,
      error: "An unexpected error occurred while updating gallery order.",
    };
  }
}

export async function deleteGalleryImage(
  imageId: string
): Promise<DeleteGalleryImageResponse> {
  const supabase = await createClient();
  const { user, error: authError } = await userService.getAuthenticatedUser(supabase);

  if (authError || !user) {
    return { success: false, error: "User not authenticated." };
  }

  try {
    // Get the current gallery data
    const { data: profileData, error: profileError } = await userService.getBusinessProfileGallery(supabase, user.id);

    if (profileError || !profileData) {
      return { success: false, error: "Failed to fetch business profile." };
    }

    // Find the image to delete
    const gallery = Array.isArray(profileData) ? (profileData as GalleryImage[]) : [];
    const imageToDelete = Array.isArray(gallery)
      ? gallery.find(img => img.id === imageId)
      : null;

    if (!imageToDelete) {
      return { success: false, error: "Image not found." };
    }
    

    // Try to delete from storage
    let storageDeleteSuccess = false;
    try {
      const { error: storageError } = await userService.removeFileFromStorage(
        supabase,
        BUCKETS.BUSINESS,
        [imageToDelete.path]
      );

      if (storageError) {
        console.error("Storage deletion error:", {
          error: storageError,
          path: imageToDelete.path,
          bucket: BUCKETS.BUSINESS
        });
      } else {
        storageDeleteSuccess = true;
      }
    } catch (error) {
      console.error("Exception during storage deletion:", error);
    }

    // Update the gallery array in business_profiles
    const updatedGallery = Array.isArray(gallery)
      ? gallery.filter(img => img.id !== imageId)
      : [];

    const { error: updateError } = await userService.updateBusinessProfile(supabase, user.id, { gallery: updatedGallery as Json });

    if (updateError) {
      return { success: false, error: `Failed to update gallery: ${updateError}` };
    }

    revalidatePath("/dashboard/business/gallery");

    // Return success with storage deletion status
    if (storageDeleteSuccess) {
      return { success: true };
    } else {
      return {
        success: true,
        warning: "Image removed from gallery, but storage cleanup may have failed"
      };
    }
  }
  catch (error) {
    console.error("Unexpected error during gallery image deletion:", error);
    return {
      success: false,
      error: `An unexpected error occurred: ${error instanceof Error ? error.message : String(error)}`,
    };
  }
}

/**
 * Get all gallery images for the current user
 */
export async function getGalleryImages(): Promise<GetGalleryImagesResponse> {
  const supabase = await createClient();
  const { user, error: authError } = await userService.getAuthenticatedUser(supabase);

  if (authError || !user) {
    return { images: [], error: "User not authenticated." };
  }

  try {
    const { data: profileData, error: profileError } = await userService.getBusinessProfileGallery(supabase, user.id);

    if (profileError) {
      return { images: [], error: `Failed to fetch business profile: ${profileError}` };
    }

    const gallery = Array.isArray(profileData) ? (profileData as GalleryImage[]) : [];
    const images = Array.isArray(gallery) ? gallery : [];

    // Return images in the order they are stored (preserves custom order)
    // If no custom order has been set, they will be in upload order
    return { images };
  } catch (error) {
    console.error("Unexpected error fetching gallery images:", error);
    return {
      images: [],
      error: `An unexpected error occurred: ${error instanceof Error ? error.message : String(error)}`,
    };
  }
}