import { getBusinessLikes, getBusinessLikesCount, getMyLikes, getMyLikesCount, getBusinessProfilesByIds } from '@/lib/supabase/services/businessService';
import { getCustomerProfilesByIds } from '@/lib/supabase/services/customerService';
import { Tables } from '@/types/supabase';
import { createClient } from "@/utils/supabase/server";

// Define interfaces for the expected data structure
interface BusinessProfileDataForLike extends Pick<Tables<"business_profiles">, 'id' | 'business_name' | 'business_slug' | 'logo_url' | 'city' | 'state' | 'pincode' | 'address_line' | 'locality'> {}

interface CustomerProfileDataForLike extends Pick<Tables<"customer_profiles">, 'id' | 'name' | 'email' | 'avatar_url'> {}

interface BusinessLikeReceivedCustomer {
  id: string;
  user_id: string;
  customer_profiles: CustomerProfileDataForLike;
  profile_type: 'customer';
}

interface BusinessLikeReceivedBusiness {
  id: string;
  user_id: string;
  business_profiles: BusinessProfileDataForLike;
  profile_type: 'business';
}

export type BusinessLikeReceived = BusinessLikeReceivedCustomer | BusinessLikeReceivedBusiness;

export interface BusinessMyLike {
  id: string;
  business_profiles: BusinessProfileDataForLike | null;
}

interface LikesReceivedResult {
  items: BusinessLikeReceived[];
  totalCount: number;
  hasMore: boolean;
  currentPage: number;
}

interface MyLikesResult {
  items: BusinessMyLike[];
  totalCount: number;
  hasMore: boolean;
  currentPage: number;
}

/**
 * Fetch likes received by a business (customers/businesses who liked this business)
 */
export async function fetchBusinessLikesReceived(
  businessId: string,
  page: number = 1,
  limit: number = 10
): Promise<LikesReceivedResult> {
  const supabase = await createClient();
  try {
    const totalCount = await getBusinessLikesCount(supabase, businessId);

    if (totalCount === 0) {
      return {
        items: [],
        totalCount: 0,
        hasMore: false,
        currentPage: page
      };
    }

    const likes = await getBusinessLikes(supabase, businessId, page, limit);

    if (!likes || likes.length === 0) {
      return {
        items: [],
        totalCount,
        hasMore: false,
        currentPage: page
      };
    }

    const userIds = likes.map((like) => like.user_id);

    const [customerProfiles, businessProfiles] = await Promise.all([
      getCustomerProfilesByIds(supabase, userIds),
      getBusinessProfilesByIds(supabase, userIds)
    ]);

    const customerProfilesMap = new Map(
      customerProfiles.data?.map((profile) => [profile.id, profile]) || []
    );
    const businessProfilesMap = new Map(
      businessProfiles.data?.map((profile) => [profile.id, profile]) || []
    );

    const processedLikes = likes
      .map((like) => {
        const customerProfile = customerProfilesMap.get(like.user_id);
        const businessProfile = businessProfilesMap.get(like.user_id);

        if (customerProfile) {
          return {
            id: like.id,
            user_id: like.user_id,
            customer_profiles: customerProfile,
            profile_type: 'customer' as const
          };
        } else if (businessProfile) {
          return {
            id: like.id,
            user_id: like.user_id,
            business_profiles: businessProfile,
            profile_type: 'business' as const
          };
        }
        return null;
      })
      .filter((item): item is BusinessLikeReceived => item !== null);

    const hasMore = totalCount > (page - 1) * limit + processedLikes.length;

    return {
      items: processedLikes,
      totalCount,
      hasMore,
      currentPage: page
    };
  } catch (error) {
    console.error('Error in fetchBusinessLikesReceived:', error);
    throw error;
  }
}

/**
 * Fetch businesses that this business has liked
 */
export async function fetchBusinessMyLikes(
  businessId: string,
  page: number = 1,
  limit: number = 10,
  searchTerm: string = ""
): Promise<MyLikesResult> {
  const supabase = await createClient();
  try {
    const totalCount = await getMyLikesCount(supabase, businessId, searchTerm);

    if (totalCount === 0) {
      return {
        items: [],
        totalCount: 0,
        hasMore: false,
        currentPage: page
      };
    }

    const likesWithProfiles = await getMyLikes(supabase, businessId, page, limit, searchTerm);

    const transformedItems: BusinessMyLike[] = (likesWithProfiles || []).map((item: { id: string; business_profiles: any; }) => ({
      id: item.id,
      business_profiles: Array.isArray(item.business_profiles)
        ? item.business_profiles[0] || null
        : item.business_profiles
    }));

    const hasMore = totalCount > (page - 1) * limit + transformedItems.length;

    return {
      items: transformedItems,
      totalCount,
      hasMore,
      currentPage: page
    };
  } catch (error) {
    console.error('Error in fetchBusinessMyLikes:', error);
    throw error;
  }
}