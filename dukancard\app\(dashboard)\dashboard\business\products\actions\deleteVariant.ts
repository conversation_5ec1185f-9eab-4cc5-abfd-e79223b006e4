"use server";

import { getAuthenticatedUser } from "@/lib/supabase/services/sharedService";
import { Tables } from "@/types/supabase";
import { getVariantDetailsWithProductBusinessId, getFilteredProductVariants, deleteProductVariant as deleteProductVariantService, deleteProductVariantsByProductId, getProductDetailsByIdAndBusinessId } from "@/lib/supabase/services/businessService";
import { removeFileFromStorage, listStorageFiles } from "@/lib/supabase/services/sharedService";
import { revalidatePath } from "next/cache";
import { getScalableUserPath } from "@/lib/utils/storage-paths";
import { BUCKETS } from "@/lib/supabase/constants";
import { createClient } from "@/utils/supabase/server";

// Delete a single product variant
export async function deleteProductVariant(
  variantId: string
): Promise<{ success: boolean; error?: string }> {
  const supabase = await createClient();
  const { user, error: authError } = await getAuthenticatedUser(supabase);
  if (authError || !user)
    return { success: false, error: "User not authenticated." };

  try {
    // First, verify that the variant belongs to the authenticated user and get product info
    const { data: variantInfo, error: variantError } = await getVariantDetailsWithProductBusinessId(supabase, variantId);

    if (variantError || !variantInfo) {
      return { success: false, error: "Variant not found." };
    }

    // Check if the product belongs to the authenticated user
    if (!variantInfo.products_services || variantInfo.products_services?.business_id !== user.id) {
      return { success: false, error: "Access denied." };
    }

    // Check if this is the last available variant for the product
    const { data: availableVariants, error: countError } = await getFilteredProductVariants(supabase, variantInfo.product_id, { includeUnavailable: false });

    if (countError) {
      console.error("Error counting variants:", countError);
      return { success: false, error: "Failed to validate variant deletion." };
    }

    // Count available variants (excluding the one being deleted)
    const availableCount = availableVariants?.filter(
      (v: { is_available: boolean; id: string }) => v.is_available && v.id !== variantId
    ).length || 0;

    // If this is the last available variant and there are other variants, prevent deletion
    if (availableCount === 0 && availableVariants && availableVariants.length > 1) {
      return {
        success: false,
        error: "Cannot delete the last available variant. At least one variant must remain available."
      };
    }

    // Delete variant images from storage before deleting the database record
    if (variantInfo.images && Array.isArray(variantInfo.images) && variantInfo.images.length > 0) {
      const bucketName = BUCKETS.BUSINESS;
      

      console.log(`Deleting ${variantInfo.images.length} images for variant ${variantId}`);

      for (const imageUrl of variantInfo.images) {
        if (imageUrl) {
          try {
            // Extract the storage path from the URL
            const url = new URL(imageUrl);
            const pathParts = url.pathname.split('/');

            // Find the 'business' part in the path
            const businessIndex = pathParts.findIndex(part => part === bucketName);

            if (businessIndex !== -1 && businessIndex < pathParts.length - 1) {
              // Extract the path after 'business/'
              const storagePath = pathParts.slice(businessIndex + 1).join('/').split('?')[0];

              // Delete the file from storage using admin client
              await removeFileFromStorage(supabase, bucketName, [storagePath]);

              console.log(`Attempted to delete image from URL: ${storagePath}`);
            }
          } catch (error) {
            console.error(`Error processing variant image URL for deletion: ${imageUrl}`, error);
          }
        }
      }

      // Also try to delete the entire variant folder
      try {
        const userPath = getScalableUserPath(user.id);
        const variantFolderPath = `${userPath}/products/${variantInfo.product_id}/${variantId}`;

        // List all files in the variant folder
        const { data: variantFiles, error: listError } = await listStorageFiles(supabase, bucketName, variantFolderPath);

        if (!listError && variantFiles && variantFiles.length > 0) {
          // Delete all files in the variant folder
          const filesToDelete = variantFiles.map((file: { name: string; }) => `${variantFolderPath}/${file.name}`);

          await removeFileFromStorage(supabase, bucketName, filesToDelete);
        }
      } catch (error) {
        console.error(`Error cleaning up variant folder for ${variantId}:`, error);
      }
    }

    // Delete the variant (database triggers will handle any additional validation)
    const { success: deleteSuccess, error: deleteError } = await deleteProductVariantService(supabase, variantId);

    if (!deleteSuccess) {
      console.error("Error deleting variant:", deleteError);
      
      // Handle specific database constraint errors
      if (deleteError?.includes("at least one variant must be available")) {
        return { success: false, error: "Cannot delete the last available variant." };
      }
      
      return { success: false, error: "Failed to delete variant." };
    }

    // Revalidate the products page
    revalidatePath("/dashboard/business/products");
    revalidatePath(`/dashboard/business/products/${variantInfo.product_id}`);

    return { success: true };
  } catch (error) {
    console.error("Unexpected error in deleteProductVariant:", error);
    return { success: false, error: "An unexpected error occurred." };
  }
}

// Delete multiple variants
export async function deleteMultipleVariants(
  variantIds: string[]
): Promise<{ success: boolean; error?: string; deleted_count?: number; failed_count?: number; errors?: string[] }> {
  const supabase = await createClient();
  const { user, error: authError } = await getAuthenticatedUser(supabase);
  if (authError || !user)
    return { success: false, error: "User not authenticated." };

  try {
    let deleted_count = 0;
    let failed_count = 0;
    const errors: string[] = [];

    const allVariantsDetails: (Tables<'product_variants'> & { products_services: { business_id: string } | null })[] = [];
    const unauthorizedVariants: string[] = [];

    for (const variantId of variantIds) {
      const { data: variantInfo, error: variantError } = await getVariantDetailsWithProductBusinessId(supabase, variantId);

      if (variantError || !variantInfo) {
        errors.push(`Variant ${variantId} not found or error fetching details.`);
        failed_count++;
        continue;
      }

      if (!variantInfo.products_services || variantInfo.products_services.business_id !== user.id) {
        unauthorizedVariants.push(variantId);
      } else {
        allVariantsDetails.push(variantInfo as Tables<'product_variants'> & { products_services: { business_id: string } });
      }
    }

    if (unauthorizedVariants.length > 0) {
      return { success: false, error: "Access denied to some variants.", failed_count: unauthorizedVariants.length, errors: unauthorizedVariants.map(id => `Access denied to variant ${id}`) };
    }

    if (allVariantsDetails.length === 0) {
      return { success: false, error: "No valid variants to delete." };
    }

    // Group by product_id
    const variantsByProduct = allVariantsDetails.reduce((acc: Record<string, (Tables<'product_variants'> & { products_services: { business_id: string } | null })[]>, variant) => {
      if (!acc[variant.product_id]) {
        acc[variant.product_id] = [];
      }
      acc[variant.product_id].push(variant);
      return acc;
    }, {} as Record<string, (Tables<'product_variants'> & { products_services: { business_id: string } })[]>);

    // For each product, check if we can safely delete the requested variants
    for (const [productId, productVariants] of Object.entries(variantsByProduct)) {
      // Get all variants for this product
      const { data: allProductVariants, error: allVariantsError } = await getFilteredProductVariants(supabase, productId);

      if (allVariantsError) {
        (productVariants as (Tables<'product_variants'> & { products_services: { business_id: string } })[]).forEach((v) => {
          errors.push(`Failed to validate deletion for variant ${v.variant_name}`);
          failed_count++;
        });
        continue;
      }

      // Calculate how many available variants will remain after deletion
      const currentAvailableCount = allProductVariants?.filter((v: { is_available: boolean }) => v.is_available).length || 0;
      const availableVariantsToDelete = (productVariants as { is_available: boolean }[]).filter((v: { is_available: boolean }) => v.is_available).length;
      const remainingAvailableCount = currentAvailableCount - availableVariantsToDelete;

      // If this would leave no available variants and there are other variants, prevent deletion
      if (remainingAvailableCount === 0 && allProductVariants && allProductVariants.length > (productVariants as (Tables<'product_variants'> & { products_services: { business_id: string } })[]).length) {
        (productVariants as (Tables<'product_variants'> & { products_services: { business_id: string } })[]).forEach((v) => {
          errors.push(`Cannot delete variant ${v.variant_name}: would leave no available variants`);
          failed_count++;
        });
        continue;
      }

      // Delete variants for this product
      for (const variant of productVariants) {
        try {
          // Delete variant images from storage first
          if (variant.images && Array.isArray(variant.images) && variant.images.length > 0) {
            const bucketName = BUCKETS.BUSINESS;


            for (const image of variant.images) {
              if (image) {
                try {
                  // Extract the storage path from the URL
                  const url = new URL(image);
                  const pathParts = url.pathname.split('/');

                  // Find the 'business' part in the path
                  const businessIndex = pathParts.findIndex(part => part === bucketName);

                  if (businessIndex !== -1 && businessIndex < pathParts.length - 1) {
                    // Extract the path after 'business/'
                    const storagePath = pathParts.slice(businessIndex + 1).join('/').split('?')[0];

                    // Delete the file from storage
                    await removeFileFromStorage(supabase, bucketName, [storagePath]);
                  }
                } catch (error) {
                  console.error(`Error deleting image for variant ${variant.variant_name}:`, error);
                }
              }
            }

            // Also try to delete the entire variant folder
            try {
              const userPath = getScalableUserPath(user.id);
              const variantFolderPath = `${userPath}/products/${variant.product_id}/${variant.id}`;

              // List and delete all files in the variant folder
              const { data: variantFiles } = await listStorageFiles(supabase, bucketName, variantFolderPath);

              if (variantFiles && variantFiles.length > 0) {
                const filesToDelete = variantFiles.map((file: { name: string; }) => `${variantFolderPath}/${file.name}`);
                await removeFileFromStorage(supabase, bucketName, filesToDelete);
              }
            } catch (error) {
              console.error(`Error cleaning up variant folder for ${variant.variant_name}:`, error);
            }
          }

          // Delete the variant from database
          const { success: deleteSuccess, error: deleteError } = await deleteProductVariantService(supabase, variant.id);

          if (!deleteSuccess) {
            errors.push(`Failed to delete variant ${variant.variant_name}: ${deleteError}`);
            failed_count++;
          } else {
            deleted_count++;
          }
        } catch (_error) {
          errors.push(`Unexpected error deleting variant ${variant.variant_name}`);
          failed_count++;
        }
      }
    }

    // Revalidate the products page
    revalidatePath("/dashboard/business/products");

    return {
      success: deleted_count > 0,
      deleted_count,
      failed_count,
      errors: errors.length > 0 ? errors : undefined,
    };
  } catch (error) {
    console.error("Unexpected error in deleteMultipleVariants:", error);
    return { success: false, error: "An unexpected error occurred." };
  }
}

// Delete all variants for a product (used when deleting a product)
export async function deleteAllProductVariants(
  productId: string
): Promise<{ success: boolean; error?: string; deleted_count?: number }> {
  const supabase = await createClient();
  const { user, error: authError } = await getAuthenticatedUser(supabase);
  if (authError || !user)
    return { success: false, error: "User not authenticated." };

  try {
    // Verify that the product belongs to the authenticated user
    const { data: product, error: productError } = await getProductDetailsByIdAndBusinessId(supabase, productId, user.id);

    if (productError || !product) {
      return { success: false, error: "Product not found or access denied." };
    }

    // Count variants before deletion
    const { data: variants, error: countError } = await getFilteredProductVariants(supabase, productId);

    if (countError) {
      console.error("Error counting variants:", countError);
    }

    // Delete all variants for the product
    const { success: deleteSuccess, error: deleteError } = await deleteProductVariantsByProductId(supabase, productId);

    if (!deleteSuccess) {
      console.error("Error deleting product variants:", deleteError);
      return { success: false, error: "Failed to delete product variants." };
    }

    return { 
      success: true, 
      deleted_count: variants?.length || 0 
    };
  } catch (error) {
    console.error("Unexpected error in deleteAllProductVariants:", error);
    return { success: false, error: "An unexpected error occurred." };
  }
}
