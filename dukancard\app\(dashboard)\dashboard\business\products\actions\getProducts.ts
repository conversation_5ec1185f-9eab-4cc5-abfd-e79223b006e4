"use server";

import { getAuthenticatedUser } from "@/lib/supabase/services/sharedService";
import { getProductsWithVariantInfo } from "@/lib/supabase/services/businessService";
import { ProductFilters, ProductSortBy } from "./types";
import { ProductWithVariantInfo } from "@/types/products";
import { TABLES } from "@/lib/supabase/constants";
import { createClient } from "@/utils/supabase/server";

// Fetch all products/services with variant information
export async function getProductServices(
  page: number = 1,
  limit: number = 10,
  filters: ProductFilters = {},
  sortBy: ProductSortBy = "created_desc"
): Promise<{
  data?: ProductWithVariantInfo[];
  count?: number;
  error?: string;
}> {
  const supabase = await createClient();
  const { user, error: authError } = await getAuthenticatedUser(supabase);
  if (authError || !user) return { error: "User not authenticated." };

  const { data, error, count } = await getProductsWithVariantInfo(supabase, user.id, page, limit, filters, sortBy);

  if (error) {
    console.error("Fetch Products Error:", error);
    return { error: "Failed to fetch products/services." };
  }

  // Transform data to include variant information
  const transformedData: ProductWithVariantInfo[] = (data || []).map(
    (product: { product_variants: { is_available: boolean }[] | null; [key: string]: unknown }) => {
      const variants =
        (product as { product_variants: { is_available: boolean }[] | null })
          .product_variants || [];
      const variant_count = variants.length;
      const available_variant_count = variants.filter(
        (v: { is_available: boolean }) => v.is_available
      ).length;
      const has_variants = variant_count > 0;

      return {
        id: product.id as string,
        business_id: product.business_id as string,
        product_type: product.product_type as "physical" | "service",
        name: product.name as string,
        description: product.description as string | null,
        base_price: product.base_price as number,
        discounted_price: product.discounted_price as number | null,
        is_available: product.is_available as boolean,
        image_url: product.image_url as string | null,
        images: product.images as string[] || [],
        featured_image_index: product.featured_image_index as number,
        slug: product.slug as string | null,
        created_at: new Date(product.created_at as string).toISOString(),
        updated_at: new Date(product.updated_at as string).toISOString(),
        variant_count,
        has_variants,
        available_variant_count,
      };
    }
  );

  // Apply variant count sorting if needed (post-processing)
  if (sortBy === "variant_count_asc") {
    transformedData.sort((a, b) => a.variant_count - b.variant_count);
  } else if (sortBy === "variant_count_desc") {
    transformedData.sort((a, b) => b.variant_count - a.variant_count);
  }

  return { data: transformedData, count: count ?? 0 };
}