"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { createClient } from "@/utils/supabase/client";
import { motion } from "framer-motion";
import { CheckCircle, Loader2, AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export default function EmailChangeSuccessPage() {
  const router = useRouter();

  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [userType, setUserType] = useState<'business' | 'customer' | null>(null);
  
  const supabase = createClient();

  useEffect(() => {
    const handleEmailChangeSuccess = async () => {
      try {
        // Get the current user to determine redirect path
        const { data: { user }, error: userError } = await supabase.auth.getUser();
        
        if (userError || !user) {
          setError("Authentication required. Please log in again.");
          setIsLoading(false);
          return;
        }

        // Determine user type based on user data or database query
        const { getPostLoginRedirectPath } = await import("@/lib/actions/redirectAfterLogin");
        const defaultPath = await getPostLoginRedirectPath(user.id);
        
        if (defaultPath.includes('/business/')) {
          setUserType('business');
        } else {
          setUserType('customer');
        }
        
        setIsLoading(false);
      } catch (err) {
        console.error('Error handling email change success:', err);
        setError("An unexpected error occurred. Please try again.");
        setIsLoading(false);
      }
    };

    handleEmailChangeSuccess();
  }, [supabase, router]);

  const handleContinueToSettings = () => {
    if (userType === 'business') {
      router.push('/dashboard/business/settings?email_change_success=true');
    } else {
      router.push('/dashboard/customer/settings?email_change_success=true');
    }
  };

  const handleGoToDashboard = () => {
    if (userType === 'business') {
      router.push('/dashboard/business');
    } else {
      router.push('/dashboard/customer');
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
        <Card className="w-full max-w-md mx-4">
          <CardContent className="pt-6">
            <div className="flex flex-col items-center space-y-4">
              <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
              <p className="text-sm text-muted-foreground">Verifying email change...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-red-50 to-pink-100 dark:from-gray-900 dark:to-gray-800">
        <Card className="w-full max-w-md mx-4">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20">
              <AlertCircle className="h-6 w-6 text-red-600" />
            </div>
            <CardTitle className="text-red-900 dark:text-red-100">Error</CardTitle>
            <CardDescription className="text-red-700 dark:text-red-300">
              {error}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={() => router.push('/login')} 
              className="w-full"
              variant="outline"
            >
              Go to Login
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 to-emerald-100 dark:from-gray-900 dark:to-gray-800">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3 }}
      >
        <Card className="w-full max-w-md mx-4">
          <CardHeader className="text-center">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
              className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-green-100 dark:bg-green-900/20"
            >
              <CheckCircle className="h-8 w-8 text-green-600" />
            </motion.div>
            <CardTitle className="text-green-900 dark:text-green-100">
              Email Updated Successfully!
            </CardTitle>
            <CardDescription className="text-green-700 dark:text-green-300">
              Your email address has been successfully changed and verified. You can now use your new email address to log in.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button 
              onClick={handleContinueToSettings} 
              className="w-full bg-green-600 hover:bg-green-700 text-white"
            >
              Continue to Settings
            </Button>
            <Button 
              onClick={handleGoToDashboard} 
              variant="outline" 
              className="w-full"
            >
              Go to Dashboard
            </Button>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
