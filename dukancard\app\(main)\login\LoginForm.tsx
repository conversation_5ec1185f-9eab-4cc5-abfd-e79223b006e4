"use client";

import { useRouter, useSearchParams } from "next/navigation";
import { useState, useEffect, useTransition } from "react";
import { motion } from "framer-motion";
import { Card } from "@/components/ui/card";
import { toast } from "sonner";
import { sendOTP, verifyOTP, loginWithMobilePassword } from "./actions";
import { EmailOTPForm } from "./components/EmailOTPForm";
import { MobilePasswordForm } from "./components/MobilePasswordForm";
import { AuthMethodToggle } from "./components/AuthMethodToggle";
import { SocialLoginButton } from "./components/SocialLoginButton";
import { createClient } from "@/utils/supabase/client";
import { getPostLoginRedirectPath } from "@/lib/actions/redirectAfterLogin";

export function LoginForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isPending, startTransition] = useTransition();
  const [redirectSlug, setRedirectSlug] = useState<string | null>(null);
  const [message, setMessage] = useState<string | null>(null);
  const [authMethod, setAuthMethod] = useState<'email-otp' | 'mobile-password'>('email-otp');
  const [step, setStep] = useState<'email' | 'otp'>('email');
  const [email, setEmail] = useState<string>('');
  const [countdown, setCountdown] = useState<number>(0);

  // Get the redirect and message parameters from the URL
  useEffect(() => {
    const redirect = searchParams.get("redirect");
    if (redirect) {
      setRedirectSlug(redirect);
    }

    const messageParam = searchParams.get("message");
    if (messageParam) {
      setMessage(messageParam);
    }
  }, [searchParams]);

  // Countdown timer for resend OTP
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [countdown]);

  // Helper function to handle post-login redirect
  async function handlePostLoginRedirect() {
    try {
      const supabase = await createClient();
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        console.error("No user found after login");
        router.push("/?view=home");
        return;
      }

      const redirectPath = await getPostLoginRedirectPath(user.id);

      // Handle redirect logic
      if (redirectSlug) {
        // IMPORTANT: Prevent open redirect vulnerabilities.
        // Ensure the redirectSlug is a relative path and not an external URL.
        if (redirectSlug.includes('://') || redirectSlug.startsWith('//')) {
          console.warn('Attempted redirect to an external or malformed URL. Redirecting to default path.');
          router.push(redirectPath);
          return;
        }

        // If it's a relative path, we can proceed.
        // The application's routing will handle the validity of the path (e.g., 404 for invalid slugs).
        router.push(`/${redirectSlug}${message ? `?message=${encodeURIComponent(message)}` : ""}`);
      } else {
        router.push(redirectPath);
      }
    } catch (error) {
      console.error("Error determining redirect path:", error);
      router.push("/?view=home");
    }
  }

  function onEmailSubmit(values: { email: string }) {
    startTransition(async () => {
      try {
        const result = await sendOTP(values);

        if (!result.success) {
          // Check if this is a configuration error (email rate limit)
          if ('isConfigurationError' in result && result.isConfigurationError) {
            toast.error("Configuration Error", {
              description: result.error,
              duration: 10000, // Show longer for configuration errors
            });
            // Don't proceed to OTP step for configuration errors
            return;
          }

          toast.error("Failed to send OTP", {
            description: result.error,
          });
          return;
        }

        toast.success("OTP sent!", {
          description: result.message,
        });

        setEmail(values.email);
        setStep('otp');
        setCountdown(60); // 60 second countdown (Supabase rate limit)
      } catch (_error) {
        toast.error("Failed to send OTP", {
          description: "An unexpected error occurred. Please try again.",
        });
      }
    });
  }

  function onOTPSubmit(values: { email: string; otp: string }) {
    startTransition(async () => {
      try {
        const result = await verifyOTP({
          email: values.email,
          otp: values.otp,
        });

        if (!result.success) {
          toast.error("OTP verification failed", {
            description: result.error,
          });
          return;
        }

        toast.success("Sign in successful!", {
          description: "Redirecting to your dashboard...",
        });

        // Use proper post-login redirect logic
        await handlePostLoginRedirect();
      } catch (_error) {
        toast.error("OTP verification failed", {
          description: "An unexpected error occurred. Please try again.",
        });
      }
    });
  }

  function handleResendOTP() {
    if (countdown > 0) return;

    // Use the same logic as onEmailSubmit for resending
    startTransition(async () => {
      try {
        const result = await sendOTP({ email });

        if (!result.success) {
          // Check if this is a configuration error (email rate limit)
          if ('isConfigurationError' in result && result.isConfigurationError) {
            toast.error("Configuration Error", {
              description: result.error,
              duration: 10000, // Show longer for configuration errors
            });
            return;
          }

          toast.error("Failed to resend OTP", {
            description: result.error,
          });
          return;
        }

        toast.success("OTP resent!", {
          description: result.message,
        });

        setCountdown(60); // Reset countdown
      } catch (_error) {
        toast.error("Failed to resend OTP", {
          description: "An unexpected error occurred. Please try again.",
        });
      }
    });
  }

  function handleBackToEmail() {
    setStep('email');
    setEmail('');
  }

  function onMobilePasswordSubmit(values: { mobile: string; password: string }) {
    startTransition(async () => {
      try {
        const result = await loginWithMobilePassword(values);

        if (!result.success) {
          toast.error("Login failed", {
            description: result.error,
          });
          return;
        }

        toast.success("Sign in successful!", {
          description: "Redirecting to your dashboard...",
        });

        // Use proper post-login redirect logic
        await handlePostLoginRedirect();
      } catch (_error) {
        toast.error("Login failed", {
          description: "An unexpected error occurred. Please try again.",
        });
      }
    });
  }

  function handleAuthMethodChange(method: 'email-otp' | 'mobile-password') {
    if (method !== authMethod) {
      setAuthMethod(method);
      setStep('email');
      // Don't reset forms - let each component manage its own state
    }
  }

  return (
    <div className="w-full max-w-[90%] sm:max-w-md md:max-w-lg">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <Card className="bg-card border border-border dark:bg-gradient-to-br dark:from-neutral-900 dark:to-black dark:border-[var(--brand-gold)]/30 p-4 sm:p-6 md:p-8 rounded-xl sm:rounded-2xl shadow-lg dark:shadow-[var(--brand-gold)]/10">
            <div className="text-center mb-6 sm:mb-8">
              <h1 className="text-xl sm:text-2xl font-bold text-foreground mb-1 sm:mb-2">
                {authMethod === 'email-otp' && step === 'otp' ? 'Enter Verification Code' : 'Welcome to Dukancard'}
              </h1>
              <p className="text-sm sm:text-base text-muted-foreground">
                {authMethod === 'email-otp' && step === 'otp'
                  ? 'Check your email for the 6-digit code'
                  : authMethod === 'email-otp'
                  ? 'Sign in or create your account with email'
                  : 'Sign in with your mobile number and password'
                }
              </p>

              {message && (
                <div className={`mt-4 p-2 sm:p-3 rounded-lg ${
                  message.toLowerCase().includes("error") || message.toLowerCase().includes("failed")
                    ? "bg-destructive/10 text-destructive"
                    : "bg-green-500/10 text-green-600 dark:text-green-400"
                }`}>
                  <p className="text-xs sm:text-sm">{message}</p>
                </div>
              )}
            </div>

            {/* Authentication Method Toggle */}
            <AuthMethodToggle
              authMethod={authMethod}
              step={step}
              onMethodChange={handleAuthMethodChange}
            />

            {/* Social Login Button */}
            <SocialLoginButton
              redirectSlug={redirectSlug}
              message={message}
              disabled={isPending}
            />

            <div className="relative mb-5 sm:mb-6">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-border" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-card px-2 text-muted-foreground">
                  {authMethod === 'email-otp' && step === 'email' ? 'Or continue with email' :
                   authMethod === 'mobile-password' ? 'Or continue with mobile' :
                   'Or use Google instead'}
                </span>
              </div>
            </div>

            {/* Form Components */}
            {authMethod === 'email-otp' ? (
              <EmailOTPForm
                step={step}
                email={email}
                countdown={countdown}
                isPending={isPending}
                onEmailSubmit={onEmailSubmit}
                onOTPSubmit={onOTPSubmit}
                onResendOTP={handleResendOTP}
                onBackToEmail={handleBackToEmail}
              />
            ) : (
              <MobilePasswordForm
                isPending={isPending}
                onSubmit={onMobilePasswordSubmit}
              />
            )}
          </Card>
        </motion.div>
    </div>
  );
}
