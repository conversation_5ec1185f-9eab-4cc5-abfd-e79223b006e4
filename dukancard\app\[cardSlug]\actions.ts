"use server";

import { getProductsWithFiltersAndPagination, getBusinessProfileStatus, insertCardVisit } from "@/lib/supabase/services/userService";

import { ProductServiceData } from "@/app/(dashboard)/dashboard/business/products/actions"; // Reuse type
import { unstable_noStore as noStore } from "next/cache";
import { COLUMNS, TABLES } from "@/lib/supabase/constants";
import { Tables } from "@/types/supabase";
import { createClient } from "@/utils/supabase/server";

// Define sort options available on the public page
export type PublicProductSortBy =
  | "created_asc"
  | "created_desc"
  | "updated_asc"
  | "updated_desc"
  | "price_asc"
  | "price_desc"
  | "name_asc"
  | "name_desc";

// Fetch subsequent pages of products for a specific business with search and filter
export async function fetchMoreProducts(
  businessId: string,
  page: number = 1,
  sortBy: PublicProductSortBy = "created_desc",
  pageSize: number = 20,
  searchTerm?: string | null,
  productType?: string | null
): Promise<{
  data?: ProductServiceData[];
  error?: string;
  totalCount?: number;
}> {
  if (!businessId) {
    return { error: "Business ID is required." };
  }
  if (page < 1) {
    return { error: "Page number must be 1 or greater." };
  }
  if (pageSize < 1) {
    return { error: "Page size must be 1 or greater." };
  }

  const supabase = await createClient();
  const { data, error, totalCount } = await getProductsWithFiltersAndPagination(
    supabase,
    businessId,
    page,
    sortBy,
    pageSize,
    searchTerm,
    productType
  );

  if (error) {
    return { error: "Failed to fetch products." };
  }

  return {
    data: (data as unknown as ProductServiceData[]) ?? [],
    totalCount: totalCount || 0,
  };
}

// --- Visit Tracking Action ---

/**
 * Parameters for the recordCardVisit function
 */
interface RecordVisitParams {
  businessProfileId: string;
  visitorIdentifier: string;
}

/**
 * Records a visit to a business card
 *
 * This function:
 * 1. Checks if the business profile is online
 * 2. Records the visit in the card_visits table
 * 3. Triggers the update_visit_counts() database function via a trigger
 *
 * Database Implementation:
 * - The card_visits table stores individual visit records with visited_at timestamp in UTC
 * - The handle_new_visit trigger calls update_visit_counts() when a new visit is recorded
 * - The update_visit_counts() function updates the pre-aggregated metrics in business_profiles:
 *   - total_visits: Total lifetime unique visitors
 *   - today_visits: Unique visitors today (based on the most recent visit date in IST)
 *   - yesterday_visits: Unique visitors yesterday (based on the day before the most recent visit date)
 *   - visits_7_days: Unique visitors in the last 7 days
 *   - visits_30_days: Unique visitors in the last 30 days
 *
 * Timezone Handling:
 * - All visit metrics are calculated based on IST (Indian Standard Time, UTC+5:30)
 * - The visited_at column in card_visits stores timestamps in UTC
 * - All database functions convert these timestamps to IST using 'visited_at AT TIME ZONE 'Asia/Kolkata''
 * - The update_visit_counts() function uses the most recent visit date in IST as "today"
 * - This ensures accurate metrics regardless of when the functions run
 *
 * Scheduled cron jobs:
 * - reset-visit-counts: Runs reset_all_visit_counts() at midnight IST (6:30 PM UTC)
 *   to reset the daily counts and update the period counts
 * - clean-card-visits: Runs clean_old_card_visits() at 1 AM IST (7:30 PM UTC)
 *   to delete visit records older than 31 days for database performance
 *
 * @param params Parameters for recording a visit
 * @returns Object indicating success or failure
 */
export async function recordCardVisit(
  params: RecordVisitParams
): Promise<{ success: boolean; error?: string }> {
  noStore();
  const { businessProfileId, visitorIdentifier } = params;

  if (!businessProfileId || !visitorIdentifier) {
    return {
      success: false,
      error: "Missing required parameters for visit tracking.",
    };
  }

  const supabase = await createClient();
  try {
    const { status, error: profileError } = await getBusinessProfileStatus(supabase, businessProfileId);

    if (profileError) {
      return { success: false, error: "Failed to verify card status." };
    }

    if (status !== "online") {
      return { success: true };
    }

    const { success: insertSuccess, error: insertError } = await insertCardVisit(supabase, {
      business_profile_id: businessProfileId,
      visitor_identifier: visitorIdentifier,
      id: crypto.randomUUID(),
      visit_date: new Date().toISOString().split('T')[0],
      visited_at: new Date().toISOString(),
    });

    if (!insertSuccess) {
      if (insertError === "23505") {
        return { success: true };
      }
      return { success: false, error: "Failed to record visit." };
    }

    return { success: true };
  } catch (_err) {
    return {
      success: false,
      error: "An unexpected error occurred during visit recording.",
    };
  }
}