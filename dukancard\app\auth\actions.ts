"use server";

import { signOutUser as signOutUserService } from "@/lib/supabase/services/userService";
import { createClient } from "@/utils/supabase/server";
import { redirect } from "next/navigation";
// Removed unused headers import

export async function signOutUser() {
  try {
    const supabase = await createClient()
    const { error: _error } = await signOutUserService(supabase);

    const cookieStore = await import("next/headers").then((m) => m.cookies());
    const cookiesToClear = ["sb-access-token", "sb-refresh-token"];

    for (const cookieName of cookiesToClear) {
      try {
        cookieStore.set(cookieName, "", {
          expires: new Date(0),
          maxAge: -1,
        });
      } catch {
      }
    }
  } catch {
  }

  return redirect("/login?logged_out=true");
}
