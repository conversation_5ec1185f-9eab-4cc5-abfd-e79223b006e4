"use server";

import { getSecureBusinessProfileBySlug as getSecureBusinessProfileBySlugService, getSecureBusinessProfileWithProductsBySlug as getSecureBusinessProfileWithProductsBySlugService } from "@/lib/supabase/services/businessService";
import {
  BusinessProfilePublicData,
  BusinessProfileWithProducts,
} from "./types";
import { createClient } from "@/utils/supabase/server";

/**
 * Securely fetch a business profile by slug using the service role key
 * This bypasses RLS and ensures sensitive data is not exposed to the client
 */
export async function getSecureBusinessProfileBySlug(slug: string): Promise<{
  data?: BusinessProfilePublicData;
  error?: string;
}> {
  if (!slug) {
    return { error: "Business slug is required." };
  }

  try {
    const supabase = await createClient()
    const { data: profileData, error: profileError } = await getSecureBusinessProfileBySlugService(supabase, slug);

    if (profileError) {
      console.error("Secure Fetch Error:", profileError);
      return {
        error: `Failed to fetch business profile: ${profileError}`,
      };
    }

    if (!profileData) {
      return { error: "Profile not found." };
    }

    const safeData: BusinessProfilePublicData = {
      ...profileData,
      subscription_status: profileData.subscription_status || null,
      plan_id: profileData.plan_id || null,
    };

    return { data: safeData };
  } catch (e) {
    console.error("Exception in getSecureBusinessProfileBySlug:", e);
    return { error: "An unexpected error occurred." };
  }
}

/**
 * Securely fetch a business profile with products by slug using the service role key
 */
export async function getSecureBusinessProfileWithProductsBySlug(
  slug: string
): Promise<{
  data?: BusinessProfileWithProducts;
  error?: string;
}> {
  if (!slug) {
    return { error: "Business slug is required." };
  }

  try {
    const supabase = await createClient()
    const { data: profileData, error: profileError } = await getSecureBusinessProfileWithProductsBySlugService(supabase, slug);

    if (profileError) {
      console.error("Secure Fetch Error:", profileError);
      return {
        error: `Failed to fetch business profile: ${profileError}`,
      };
    }

    if (!profileData) {
      return { error: "Profile not found." };
    }

    const safeData: BusinessProfileWithProducts = {
      ...profileData,
      products_services: (profileData as any).products_services || [],
    };

    return { data: safeData };
  } catch (e) {
    console.error(
      "Exception in getSecureBusinessProfileWithProductsBySlug:",
      e
    );
    return { error: "An unexpected error occurred." };
  }
}