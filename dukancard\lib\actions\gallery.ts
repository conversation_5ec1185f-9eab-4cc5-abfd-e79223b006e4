"use server";

import { getBusinessProfileGallery, getBusinessProfileIdAndStatusBySlug, getPublicSubscriptionStatus } from "@/lib/supabase/services/userService";
import { SupabaseClient } from "@supabase/supabase-js";
import { Database } from "@/types/supabase";
import { getGalleryLimit } from "@/app/(dashboard)/dashboard/business/gallery/utils";
import { Tables } from "@/types/supabase";

type BusinessProfiles = Tables<'business_profiles'>;
type PaymentSubscriptions = Tables<'payment_subscriptions'>;

import { GalleryImage } from "@/types/gallery";
import { createClient } from "@/utils/supabase/server";

/**
 * Get gallery images for a specific business
 * @param businessId The business ID
 * @returns Gallery images
 */
export async function getBusinessGalleryImages(businessId: string): Promise<{
  images: GalleryImage[];
  error?: string;
}> {
  try {
    const supabase = await createClient()
    const { data: galleryData, error } = await getBusinessProfileGallery(supabase, businessId);

    if (error) {
      console.error("Error fetching business profile:", error);
      return {
        images: [],
        error: `Failed to fetch gallery images: ${error}`,
      };
    }

    const gallery = Array.isArray(galleryData) ? (galleryData as unknown as GalleryImage[]) : [];
    const images = Array.isArray(gallery) ? gallery : [];

    images.sort((a, b) => {
      return (
        new Date((b as any).created_at).getTime() - new Date((a as any).created_at).getTime()
      );
    });

    return { images: images as unknown as GalleryImage[] };
  } catch (error) {
    console.error("Unexpected error fetching gallery images:", error);
    return {
      images: [],
      error: `An unexpected error occurred: ${
        error instanceof Error ? error.message : String(error)
      }`,
    };
  }
}

/**
 * Get gallery images for a business by slug (limited to 4 for gallery tab)
 * @param businessSlug The business slug
 * @returns Gallery images (max 4) and total count
 */
export async function getBusinessGalleryImagesForTab(
  businessSlug: string
): Promise<{
  images: GalleryImage[];
  totalCount: number;
  error?: string;
}> {
  try {
    const supabase = await createClient()
    const { data: business, error: businessError } = await getBusinessProfileIdAndStatusBySlug(supabase, businessSlug);

    if (businessError || !business) {
      console.error("Error fetching business profile:", businessError);
      return { images: [], totalCount: 0, error: "Business not found" };
    }

    if (business.status !== "online") {
      return { images: [], totalCount: 0, error: "Business is not online" };
    }

    const { planId, error: subscriptionError } = await getPublicSubscriptionStatus(supabase, business.id);

    if (subscriptionError) {
      console.error("Error fetching subscription data:", subscriptionError);
    }

    const planIdValue = planId || "free";

    const galleryLimit = getGalleryLimit(planIdValue);

    const { data: galleryData, error: galleryError } = await getBusinessProfileGallery(supabase, business.id);

    if (galleryError) {
      console.error("Error fetching business gallery:", galleryError);
      return {
        images: [],
        totalCount: 0,
        error: `Failed to fetch gallery images: ${galleryError}`,
      };
    }

    const gallery = Array.isArray(galleryData) ? (galleryData as unknown as GalleryImage[]) : [];
    const images = Array.isArray(gallery) ? gallery : [];

    images.sort((a, b) => {
      return (
        new Date((b as any).created_at).getTime() - new Date((a as any).created_at).getTime()
      );
    });

    const planLimitedImages = images.slice(0, galleryLimit);
    const finalLimit = Math.min(planLimitedImages.length, 4);
    const limitedImages = planLimitedImages.slice(0, finalLimit);

    const totalCount = planLimitedImages.length;

    return { images: limitedImages as unknown as GalleryImage[], totalCount };
  } catch (error) {
    console.error("Unexpected error fetching gallery images for tab:", error);
    return {
      images: [],
      totalCount: 0,
      error: `An unexpected error occurred: ${
        error instanceof Error ? error.message : String(error)
      }`,
    };
  }
}

/**
 * Get gallery images for a business by slug
 * @param businessSlug The business slug
 * @returns Gallery images
 */
export async function getBusinessGalleryImagesBySlug(
  businessSlug: string
): Promise<{
  images: GalleryImage[];
  error?: string;
}> {
  try {
    const supabase = await createClient()
    const { data: business, error: businessError } = await getBusinessProfileIdAndStatusBySlug(supabase, businessSlug);

    if (businessError || !business) {
      console.error("Error fetching business profile:", businessError);
      return { images: [], error: "Business not found" };
    }

    if (business.status !== "online") {
      return { images: [], error: "Business is not online" };
    }

    const { planId, error: subscriptionError } = await getPublicSubscriptionStatus(supabase, business.id);

    if (subscriptionError) {
      console.error("Error fetching subscription data:", subscriptionError);
    }

    const planIdValue = planId || "free";

    const galleryLimit = getGalleryLimit(planIdValue);

    const { data: galleryData, error: galleryError } = await getBusinessProfileGallery(supabase, business.id);

    if (galleryError) {
      console.error("Error fetching business gallery:", galleryError);
      return {
        images: [],
        error: `Failed to fetch gallery images: ${galleryError}`,
      };
    }

    const gallery = Array.isArray(galleryData) ? (galleryData as unknown as GalleryImage[]) : [];
    const images = Array.isArray(gallery) ? gallery : [];

    images.sort((a, b) => {
      return (
        new Date((b as any).created_at).getTime() - new Date((a as any).created_at).getTime()
      );
    });

    const limitedImages = images.slice(0, galleryLimit);

    return { images: limitedImages as unknown as GalleryImage[] };
  } catch (error) {
    console.error("Unexpected error fetching gallery images by slug:", error);
    return {
      images: [],
      error: `An unexpected error occurred: ${
        error instanceof Error ? error.message : String(error)
      }`,
    };
  }
}

/**
 * Get paginated gallery images for a business by slug
 * @param businessSlug The business slug
 * @param page Page number (1-based)
 * @param limit Number of images per page
 * @returns Paginated gallery images with metadata
 */
export async function getBusinessGalleryImagesPaginated(
  businessSlug: string,
  page: number = 1,
  limit: number = 20
): Promise<{
  images: GalleryImage[];
  totalCount: number;
  totalPages: number;
  currentPage: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
  error?: string;
}> {
  try {
    const supabase = await createClient()
    const { data: business, error: businessError } = await getBusinessProfileIdAndStatusBySlug(supabase, businessSlug);

    if (businessError || !business) {
      console.error("Error fetching business profile:", businessError);
      return {
        images: [],
        totalCount: 0,
        totalPages: 0,
        currentPage: page,
        hasNextPage: false,
        hasPrevPage: false,
        error: "Business not found",
      };
    }

    if (business.status !== "online") {
      return {
        images: [],
        totalCount: 0,
        totalPages: 0,
        currentPage: page,
        hasNextPage: false,
        hasPrevPage: false,
        error: "Business is not online",
      };
    }

    const { planId, error: subscriptionError } = await getPublicSubscriptionStatus(supabase, business.id);

    if (subscriptionError) {
      console.error("Error fetching subscription data:", subscriptionError);
    }

    const planIdValue = planId || "free";

    const galleryLimit = getGalleryLimit(planIdValue);

    const { data: galleryData, error: galleryError } = await getBusinessProfileGallery(supabase, business.id);

    if (galleryError) {
      console.error("Error fetching business gallery:", galleryError);
      return {
        images: [],
        totalCount: 0,
        totalPages: 0,
        currentPage: page,
        hasNextPage: false,
        hasPrevPage: false,
        error: `Failed to fetch gallery images: ${galleryError}`,
      };
    }

    const gallery = Array.isArray(galleryData) ? (galleryData as unknown as GalleryImage[]) : [];
    const allImages = Array.isArray(gallery) ? gallery : [];

    allImages.sort((a, b) => {
      return (
        new Date((b as any).created_at).getTime() - new Date((a as any).created_at).getTime()
      );
    });

    const planLimitedImages = allImages.slice(0, galleryLimit);
    const totalCount = planLimitedImages.length;

    const totalPages = Math.ceil(totalCount / limit);
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;

    const paginatedImages = planLimitedImages.slice(startIndex, endIndex);

    return {
      images: paginatedImages as unknown as GalleryImage[],
      totalCount,
      totalPages,
      currentPage: page,
      hasNextPage: page < totalPages,
      hasPrevPage: page > 1,
    };
  } catch (error) {
    console.error("Unexpected error fetching paginated gallery images:", error);
    return {
      images: [],
      totalCount: 0,
      totalPages: 0,
      currentPage: page,
      hasNextPage: false,
      hasPrevPage: false,
      error: `An unexpected error occurred: ${
        error instanceof Error ? error.message : String(error)
      }`,
    };
  }
}
