import { TABLES, COLUMNS } from "../../supabase/constants";
import { Database, Tables } from "../../../types/supabase";
import { getAuthenticatedUser, fetchUserSubscriptions, getCustomerProfileLocation, getBusinessProfileLocation, getUnifiedPosts } from "../../supabase/services/userService";


import { FeedQueryParams } from '@/lib/types/posts';
import { processOptimizedHybrid } from '@/lib/utils/feed/optimizedHybridAlgorithm';
import {
  processFeedWithCreationHandling,
  PostCreationState,
  EnhancedFeedResponse
} from '@/lib/utils/feed/postCreationHandler';
import { createClient } from "@/utils/supabase/server";

export interface UnifiedPost {
  id: string | null;
  post_source: 'business' | 'customer' | string | null;
  author_id: string | null;
  content: string | null;
  image_url: string | null;
  created_at: string;
  updated_at: string;
  city_slug: string | null;
  state_slug: string | null;
  locality_slug: string | null;
  pincode: string | null;
  product_ids: string[];
  mentioned_business_ids: string[];
  author_name: string | null;
  author_avatar: string | null;
  business_slug: string | null; // Business slug for business posts, null for customer posts
  phone: string | null; // Phone number for business posts, null for customer posts
  whatsapp_number: string | null; // WhatsApp number for business posts, null for customer posts
  business_plan: string | null; // Plan for business posts, null for customer posts
}

export interface UnifiedFeedResponse {
  success: boolean;
  message: string;
  error?: string;
  data?: {
    items: UnifiedPost[];
    totalCount: number;
    hasMore: boolean;
  };
}

/**
 * Get unified feed posts (business + customer posts) with proper pagination
 * Supports post creation state for immediate feedback when user creates a post
 */
export async function getUnifiedFeedPosts(
  params: FeedQueryParams,
  creationState?: PostCreationState
): Promise<EnhancedFeedResponse> {
  
  const {
    filter = 'smart',
    page = 1,
    limit = 10,
    city_slug,
    state_slug,
    locality_slug,
    pincode
  } = params;

  const supabase = await createClient()

  try {
    // Get current user for smart and subscribed filters
    const { user, error: authError } = await getAuthenticatedUser(supabase);
    if (authError || !user) {
      // Handle unauthenticated user case, perhaps return an empty feed or an error
      return {
        success: false,
        message: authError || "User not authenticated.",
        error: authError || "User not authenticated.",
        data: {
          items: [],
          totalCount: 0,
          hasMore: false,
          hasJustCreatedPost: false
        }
      };
    }

    // Build base query
    let conditions: string[] = [];

    // Apply filters based on filter type
    switch (filter) {
      case 'smart':
        if (user) {
          // Get user's subscribed businesses for smart feed
          const { data: subscriptions } = await fetchUserSubscriptions(supabase, user.id);

          const subscribedBusinessIds = subscriptions?.map((s: any) => s.business_profile_id) || [];

          // Try to get user's location from both customer and business profiles
          const [customerProfile, businessProfile] = await Promise.all([
            getCustomerProfileLocation(supabase, user.id),
            getBusinessProfileLocation(supabase, user.id)
          ]);

          // Use whichever profile exists
          const userProfile = customerProfile.data || businessProfile.data;

          // Build smart feed conditions
          const conditions = [];

          // Subscribed businesses
          if (subscribedBusinessIds.length > 0) {
            conditions.push(`and(post_source.eq.business,author_id.in.(${subscribedBusinessIds.join(',')}))`);
          }

          // User's own posts (check both customer and business posts)
          conditions.push(`and(post_source.eq.customer,author_id.eq.${user.id})`);
          conditions.push(`and(post_source.eq.business,author_id.eq.${user.id})`);

          // Local posts based on user location
          if (userProfile?.locality_slug) {
            conditions.push(`${COLUMNS.LOCALITY_SLUG}.eq.${userProfile.locality_slug}`);
          }
          if (userProfile?.pincode) {
            conditions.push(`${COLUMNS.PINCODE}.eq.${userProfile.pincode}`);
          }
          if (userProfile?.city_slug) {
            conditions.push(`${COLUMNS.CITY_SLUG}.eq.${userProfile.city_slug}`);
          }

          
        }
        break;

      case 'subscribed':
        if (user) {
          const { data: subscriptions } = await fetchUserSubscriptions(supabase, user.id);

          const subscribedBusinessIds = subscriptions?.map(s => s.business_profile_id) || [];

          if (subscribedBusinessIds.length > 0) {
            conditions.push(`and(post_source.eq.business,author_id.in.(${subscribedBusinessIds.join(',')}))`);
          } else {
            // No subscriptions, return empty result
            return {
              success: true,
              message: 'No subscribed businesses found',
              data: {
                items: [],
                totalCount: 0,
                hasMore: false,
                hasJustCreatedPost: false
              }
            };
          }
        }
        break;

      case 'locality':
        if (locality_slug) {
          conditions.push(`${COLUMNS.LOCALITY_SLUG}.eq.${locality_slug}`);
        } else if (user) {
          // If no locality_slug provided, get user's locality from their profile
          const [customerProfile, businessProfile] = await Promise.all([
            getCustomerProfileLocation(supabase, user.id),
            getBusinessProfileLocation(supabase, user.id)
          ]);
          const userLocality = customerProfile.data?.locality_slug || businessProfile.data?.locality_slug;
          if (userLocality) {
            conditions.push(`${COLUMNS.LOCALITY_SLUG}.eq.${userLocality}`);
          }
        }
        break;

      case 'pincode':
        if (pincode) {
          conditions.push(`${COLUMNS.PINCODE}.eq.${pincode}`);
        } else if (user) {
          // If no pincode provided, get user's pincode from their profile
          const [customerProfile, businessProfile] = await Promise.all([
            getCustomerProfileLocation(supabase, user.id),
            getBusinessProfileLocation(supabase, user.id)
          ]);
          const userPincode = customerProfile.data?.pincode || businessProfile.data?.pincode;
          if (userPincode) {
            conditions.push(`${COLUMNS.PINCODE}.eq.${userPincode}`);
          }
        }
        break;

      case 'city':
        if (city_slug) {
          conditions.push(`${COLUMNS.CITY_SLUG}.eq.${city_slug}`);
        } else if (user) {
          // If no city_slug provided, get user's city from their profile
          const [customerProfile, businessProfile] = await Promise.all([
            getCustomerProfileLocation(supabase, user.id),
            getBusinessProfileLocation(supabase, user.id)
          ]);
          const userCity = customerProfile.data?.city_slug || businessProfile.data?.city_slug;
          if (userCity) {
            conditions.push(`${COLUMNS.CITY_SLUG}.eq.${userCity}`);
          }
        }
        break;

      case 'state':
        if (state_slug) {
          conditions.push(`${COLUMNS.STATE_SLUG}.eq.${state_slug}`);
        } else if (user) {
          // If no state_slug provided, get user's state from their profile
          const [customerProfile, businessProfile] = await Promise.all([
            getCustomerProfileLocation(supabase, user.id),
            getBusinessProfileLocation(supabase, user.id)
          ]);
          const userState = customerProfile.data?.state_slug || businessProfile.data?.state_slug;
          if (userState) {
            conditions.push(`${COLUMNS.STATE_SLUG}.eq.${userState}`);
          }
        }
        break;

      case 'all':
        // No additional filters for 'all'
        break;
    }

    // Fetch exactly the target number of posts to prevent post loss
    // Algorithm will arrange these posts optimally without losing any content
    const from = (page - 1) * limit; // Standard pagination
    const to = from + limit - 1;

    // Execute query with chronological ordering (prioritization applied client-side)
    const { data, error, count } = await getUnifiedPosts(supabase, from, to, conditions);

    if (error) {
      console.error('Error fetching unified feed posts:', error);
      return {
        success: false,
        message: 'Failed to fetch posts',
        error: (error as any)?.message || 'Unknown error'
      };
    }

    // Apply optimized hybrid algorithm to ALL feed types
    // Processes exactly the fetched posts without losing any content
    // Business posts get plan prioritization, customer posts maintain chronological order
    // Works with location filters (locality, pincode, city, state, all)
    // Filter out posts with invalid data and ensure required fields are non-null
    const validData = data ? data.filter((post: any) =>
      post.id &&
      post.created_at &&
      post.updated_at
    ).map((post: any) => ({
      ...post,
      created_at: post.created_at || new Date().toISOString(),
      updated_at: post.updated_at || new Date().toISOString()
    })) : [];

    const prioritizedData = validData.length > 0 ? processOptimizedHybrid(validData, {
      enableDiversity: true,
      maintainChronologicalFlow: true
    }) : [];

    const totalCount = count || 0;
    // Standard pagination logic - no posts lost
    const hasMore = prioritizedData.length === limit && (from + limit) < totalCount;

    // Handle post creation state for immediate feedback
    return processFeedWithCreationHandling(
      prioritizedData,
      totalCount,
      hasMore,
      creationState
    );

  } catch (error) {
    console.error('Unexpected error in getUnifiedFeedPosts:', error);
    return {
      success: false,
      message: 'An unexpected error occurred',
      error: (error as Error).message
    };
  }
}

/**
 * Get unified feed posts with author information populated
 * Author information is now included directly in the unified_posts view
 */
export async function getUnifiedFeedPostsWithAuthors(
  params: FeedQueryParams,
  creationState?: PostCreationState
): Promise<EnhancedFeedResponse> {
  // Since author information is now included in the unified_posts view,
  // we can just return the result from getUnifiedFeedPosts directly
  return await getUnifiedFeedPosts(params, creationState);
}