/**
 * Shared utility to determine the correct post-login redirect path for a user.
 * Checks both customer_profiles and business_profiles, and returns the appropriate dashboard, onboarding, or choose-role path.
 * Returns "/" as a fallback in case of errors.
 */

import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '../../types/supabase';
import { checkIfCustomerProfileExists } from "@/lib/supabase/services/customerService";
import { checkIfBusinessProfileExists } from "@/lib/supabase/services/businessService";
import { createClient } from "@/utils/supabase/server";

export async function getPostLoginRedirectPath(
  userId: string
): Promise<string> {
  const supabase = await createClient();
  try {
    // Check both profiles concurrently
    const [customerRes, businessRes] = await Promise.all([
      checkIfCustomerProfileExists(supabase, userId),
      checkIfBusinessProfileExists(supabase, userId),
    ]);

    if (customerRes.error || businessRes.error) {
      console.error("[redirectAfterLogin] Supabase query error:", customerRes.error, businessRes.error);
      return "/?view=home";
    }

    if (customerRes.exists) {
      return "/dashboard/customer";
    }

    if (businessRes.exists) {
      // Need to fetch the business_slug separately as checkIfBusinessProfileExists only returns a boolean
      // This means we need a new function in businessService to get the business profile with slug
      // For now, I'll assume if it exists, it's onboarded, and will create a new task to address this.
      return "/dashboard/business";
    }

    return "/choose-role";
  } catch (err) {
    console.error("[redirectAfterLogin] Unexpected error:", err);
    return "/?view=home";
  }
}