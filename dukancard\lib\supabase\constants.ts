// lib/supabase/constants.ts

export const TABLES = {
  BLOGS: "blogs",
  BUSINESS_ACTIVITIES: "business_activities",
  BUSINESS_PROFILES: "business_profiles",
  CARD_VISITS: "card_visits",
  CUSTOM_ADS: "custom_ads",
  CUSTOM_AD_TARGETS: "custom_ad_targets",
  CUSTOMER_POSTS: "customer_posts",
  CUSTOMER_PROFILES: "customer_profiles",
  CUSTOMER_PROFILES_PUBLIC: "customer_profiles_public",
  LIKES: "likes",
  PAYMENT_SUBSCRIPTIONS: "payment_subscriptions",
  PINCODES: "pincodes",
  PRODUCTS_SERVICES: "products_services",
  PRODUCT_VARIANTS: "product_variants",
  PUBLIC_SUBSCRIPTION_STATUS: "public_subscription_status",
  STORAGE_CLEANUP_CONFIG: "storage_cleanup_config",
  STORAGE_CLEANUP_PROGRESS: "storage_cleanup_progress",
  SUBSCRIPTIONS: "subscriptions",
  SYSTEM_ALERTS: "system_alerts",
  UNIFIED_POSTS: "unified_posts",
  RATINGS_REVIEWS: "ratings_reviews",
} as const;

export const BUCKETS = {
  BUSINESS: "business",
  CUSTOMERS: "customers",
} as const;

export const COLUMNS = {
  ID: "id",
  CREATED_AT: "created_at",
  UPDATED_AT: "updated_at",
  NAME: "name",
  EMAIL: "email",
  PHONE: "phone",
  CITY: "city",
  STATE: "state",
  PINCODE: "pincode",
  PLAN_ID: "plan_id",
  LOCALITY: "locality",
  CITY_SLUG: "city_slug",
  STATE_SLUG: "state_slug",
  LOCALITY_SLUG: "locality_slug",
  AVATAR_URL: "avatar_url",
  LOGO_URL: "logo_url",
  IMAGE_URL: "image_url",
  IMAGES: "images",
  SLUG: "slug",
  STATUS: "status",
  CONTENT: "content",
  GALLERY: "gallery",
  DESCRIPTION: "description",
  TITLE: "title",
  USER_ID: "user_id",
  BUSINESS_ID: "business_id",
  BUSINESS_NAME: "business_name",
  BUSINESS_SLUG: "business_slug",
  PRODUCT_ID: "product_id",
  LATITUDE: "latitude",
  LONGITUDE: "longitude",
  PRODUCT_TYPE: "product_type",
  BASE_PRICE: "base_price",
  DISCOUNTED_PRICE: "discounted_price",
  IS_AVAILABLE: "is_available",
  CUSTOM_AD_TARGETS: "custom_ad_targets",
  AD_IMAGE_URL: "ad_image_url",
  AD_LINK_URL: "ad_link_url",
  IS_ACTIVE: "is_active",
  TARGETING_LOCATIONS: "targeting_locations",
  RATINGS_REVIEWS: "ratings_reviews",
  BUSINESS_PROFILE_ID: "business_profile_id",
  RAZORPAY_SUBSCRIPTION_ID: "razorpay_subscription_id",
  SUBSCRIPTION_STATUS: "subscription_status",
  TOTAL_LIKES: "total_likes",
  TOTAL_SUBSCRIPTIONS: "total_subscriptions",
  AVERAGE_RATING: "average_rating",
  TOTAL_VISITS: "total_visits",
  TODAY_VISITS: "today_visits",
  YESTERDAY_VISITS: "yesterday_visits",
  VISITS_7_DAYS: "visits_7_days",
  VISITS_30_DAYS: "visits_30_days",
  CUSTOM_ADS: "custom_ads",
  CUSTOM_BRANDING: "custom_branding",
  CONTACT_EMAIL: "contact_email",
  HAS_ACTIVE_SUBSCRIPTION: "has_active_subscription",
  TRIAL_END_DATE: "trial_end_date",
  MEMBER_NAME: "member_name",
  ADDRESS_LINE: "address_line",
  INSTAGRAM_URL: "instagram_url",
  FACEBOOK_URL: "facebook_url",
  WHATSAPP_NUMBER: "whatsapp_number",
  ABOUT_BIO: "about_bio",
  THEME_COLOR: "theme_color",
  DELIVERY_INFO: "delivery_info",
  BUSINESS_HOURS: "business_hours",
  BUSINESS_CATEGORY: "business_category",
  ESTABLISHED_YEAR: "established_year",
  VARIANT_VALUES: "variant_values",
  VARIANT_NAME: "variant_name",
  FEATURED_IMAGE_INDEX: "featured_image_index",
  STATE_NAME: "StateName",
  DIVISION_NAME: "DivisionName",
} as const;

export const RPC_FUNCTIONS = {
  GET_DAILY_UNIQUE_VISIT_TREND: "get_daily_unique_visit_trend",
  GET_HOURLY_UNIQUE_VISIT_TREND: "get_hourly_unique_visit_trend",
  GET_MONTHLY_UNIQUE_VISITS: "get_monthly_unique_visits",
  GET_MONTHLY_UNIQUE_VISIT_TREND: "get_monthly_unique_visit_trend",
  GET_AVAILABLE_YEARS_FOR_MONTHLY_METRICS: "get_available_years_for_monthly_metrics",
  GET_TOTAL_UNIQUE_VISITS: "get_total_unique_visits",
  GET_AD_FOR_PINCODE: "get_ad_for_pincode",
  GET_PRODUCT_WITH_VARIANTS: "get_product_with_variants",
  GET_AVAILABLE_PRODUCT_VARIANTS: "get_available_product_variants",
  GET_BUSINESS_VARIANT_STATS: "get_business_variant_stats",
  IS_VARIANT_COMBINATION_UNIQUE: "is_variant_combination_unique",
} as const;

export const RPC_PARAMS = {
    BUSINESS_ID: "business_id",
    START_DATE: "start_date",
    END_DATE: "end_date",
    TARGET_DATE: "target_date",
    TARGET_YEAR: "target_year",
    TARGET_MONTH: "target_month",
    START_YEAR: "start_year",
    START_MONTH: "start_month",
    END_YEAR: "end_year",
    END_MONTH: "end_month",
    TARGET_PINCODE: "target_pincode",
} as const;
