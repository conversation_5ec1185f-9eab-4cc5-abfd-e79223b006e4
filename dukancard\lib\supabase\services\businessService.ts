import { SupabaseClient } from "@supabase/supabase-js";
import { TABLES, COLUMNS, RPC_FUNCTIONS, RPC_PARAMS } from "../constants";
import { Tables, TablesInsert, TablesUpdate, Json } from "../../../types/supabase";

type BusinessProfileAnalyticsRow = Pick<Tables<'business_profiles'>, 'total_visits' | 'today_visits' | 'yesterday_visits' | 'visits_7_days' | 'visits_30_days'>;

/**
 * Checks if a business profile exists for a given user ID.
 * @param userId The ID of the user to check.
 * @returns An object containing a boolean indicating if the profile exists and an error if one occurred.
 */
export async function checkIfBusinessProfileExists(supabase: SupabaseClient, userId: string) {
  try {
    const { data, error } = await supabase
      .from(TABLES.BUSINESS_PROFILES)
      .select(COLUMNS.ID)
      .eq(COLUMNS.ID, userId)
      .maybeSingle();

    if (error) {
      console.error(`Error checking existing business profile: ${error.message}`);
      return { exists: false, error: "Database error checking business profile." };
    }

    return { exists: !!data, error: null };
  } catch (err) {
    console.error(`Unexpected error checking business profile: ${err}`);
    return { exists: false, error: "An unexpected error occurred." };
  }
}

/**
 * Fetches analytics data for a business profile.
 * @param businessProfileId The ID of the business profile.
 * @returns An object containing the business profile analytics data or an error.
 */
export async function getBusinessProfileAnalyticsData(supabase: SupabaseClient, businessProfileId: string): Promise<{ data: BusinessProfileAnalyticsRow | null; error: string | null }> {
  try {
    const { data, error } = await supabase
      .from(TABLES.BUSINESS_PROFILES)
      .select(`${COLUMNS.TOTAL_VISITS}, ${COLUMNS.TODAY_VISITS}, ${COLUMNS.YESTERDAY_VISITS}, ${COLUMNS.VISITS_7_DAYS}, ${COLUMNS.VISITS_30_DAYS}`)
      .eq(COLUMNS.ID, businessProfileId)
      .single();

    if (error) {
      console.error(`Error fetching business profile analytics data: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data: data as BusinessProfileAnalyticsRow, error: null };
  } catch (err) {
    console.error(`Unexpected error fetching business profile analytics data: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Calls the 'get_daily_unique_visit_trend' RPC function.
 * @param businessId The ID of the business.
 * @param startDate The start date for the trend (YYYY-MM-DD).
 * @param endDate The end date for the trend (YYYY-MM-DD).
 * @returns An object containing the daily unique visit trend data or an error.
 */
export async function getDailyUniqueVisitTrend(supabase: SupabaseClient, businessId: string, startDate: string, endDate: string) {
  try {
    const { data, error } = await supabase.rpc(RPC_FUNCTIONS.GET_DAILY_UNIQUE_VISIT_TREND, {
      [RPC_PARAMS.BUSINESS_ID]: businessId,
      [RPC_PARAMS.START_DATE]: startDate,
      [RPC_PARAMS.END_DATE]: endDate,
    });
    if (error) {
      console.error(`Error fetching daily unique visit trend: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error(`Unexpected error fetching daily unique visit trend: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Calls the 'get_hourly_unique_visit_trend' RPC function.
 * @param businessId The ID of the business.
 * @param targetDate The target date for the hourly trend (YYYY-MM-DD).
 * @returns An object containing the hourly unique visit trend data or an error.
 */
export async function getHourlyUniqueVisitTrend(supabase: SupabaseClient, businessId: string, targetDate: string) {
  try {
    const { data, error } = await supabase.rpc(RPC_FUNCTIONS.GET_HOURLY_UNIQUE_VISIT_TREND, {
      [RPC_PARAMS.BUSINESS_ID]: businessId,
      [RPC_PARAMS.TARGET_DATE]: targetDate,
    });
    if (error) {
      console.error(`Error fetching hourly unique visit trend: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error(`Unexpected error fetching hourly unique visit trend: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Calls the 'get_monthly_unique_visits' RPC function.
 * @param businessId The ID of the business.
 * @param targetYear The target year.
 * @param targetMonth The target month.
 * @returns An object containing the monthly unique visits count or an error.
 */
export async function getMonthlyUniqueVisits(supabase: SupabaseClient, businessId: string, targetYear: number, targetMonth: number) {
  try {
    const { data, error } = await supabase.rpc(RPC_FUNCTIONS.GET_MONTHLY_UNIQUE_VISITS, {
      [RPC_PARAMS.BUSINESS_ID]: businessId,
      [RPC_PARAMS.TARGET_YEAR]: targetYear,
      [RPC_PARAMS.TARGET_MONTH]: targetMonth,
    });
    if (error) {
      console.error(`Error fetching monthly unique visits: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error(`Unexpected error fetching monthly unique visits: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Calls the 'get_monthly_unique_visit_trend' RPC function.
 * @param businessId The ID of the business.
 * @param startYear The start year for the trend.
 * @param startMonth The start month for the trend.
 * @param endYear The end year for the trend.
 * @param endMonth The end month for the trend.
 * @returns An object containing the monthly unique visit trend data or an error.
 */
export async function getMonthlyUniqueVisitTrend(supabase: SupabaseClient, businessId: string, startYear: number, startMonth: number, endYear: number, endMonth: number) {
  try {
    const { data, error } = await supabase.rpc(RPC_FUNCTIONS.GET_MONTHLY_UNIQUE_VISIT_TREND, {
      [RPC_PARAMS.BUSINESS_ID]: businessId,
      [RPC_PARAMS.START_YEAR]: startYear,
      [RPC_PARAMS.START_MONTH]: startMonth,
      [RPC_PARAMS.END_YEAR]: endYear,
      [RPC_PARAMS.END_MONTH]: endMonth,
    });
    if (error) {
      console.error(`Error fetching monthly unique visit trend: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error(`Unexpected error fetching monthly unique visit trend: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Calls the 'get_available_years_for_monthly_metrics' RPC function.
 * @param businessId The ID of the business.
 * @returns An object containing the available years for monthly metrics or an error.
 */
export async function getAvailableYearsForMonthlyMetrics(supabase: SupabaseClient, businessId: string) {
  try {
    const { data, error } = await supabase.rpc(RPC_FUNCTIONS.GET_AVAILABLE_YEARS_FOR_MONTHLY_METRICS, {
      [RPC_PARAMS.BUSINESS_ID]: businessId,
    });
    if (error) {
      console.error(`Error fetching available years for monthly metrics: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error(`Unexpected error fetching available years for monthly metrics: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Calls the 'get_total_unique_visits' RPC function.
 * @param businessId The ID of the business.
 * @returns An object containing the total unique visits count or an error.
 */
export async function getTotalUniqueVisits(supabase: SupabaseClient, businessId: string) {
  try {
    const { data, error } = await supabase.rpc(RPC_FUNCTIONS.GET_TOTAL_UNIQUE_VISITS, {
      [RPC_PARAMS.BUSINESS_ID]: businessId,
    });
    if (error) {
      console.error(`Error fetching total unique visits: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error(`Unexpected error fetching total unique visits: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Fetches a business profile along with its interaction metrics.
 * @param userId The ID of the user to fetch the profile for.
 * @returns An object containing the business profile data or an error.
 */
export async function getBusinessProfileWithInteractionMetrics(supabase: SupabaseClient, userId: string) {
  try {
    const { data, error } = await supabase
      .from(TABLES.BUSINESS_PROFILES)
      .select(`${COLUMNS.TOTAL_LIKES}, ${COLUMNS.TOTAL_SUBSCRIPTIONS}, ${COLUMNS.AVERAGE_RATING}, ${COLUMNS.TOTAL_VISITS}, ${COLUMNS.TODAY_VISITS}, ${COLUMNS.YESTERDAY_VISITS}, ${COLUMNS.VISITS_7_DAYS}, ${COLUMNS.VISITS_30_DAYS}`)
      .eq(COLUMNS.ID, userId)
      .single();

    if (error) {
      console.error(`Error fetching business profile with interaction metrics: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error(`Unexpected error fetching business profile with interaction metrics: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Fetches the latest subscription for a business.
 * @param userId The ID of the user to fetch the subscription for.
 * @returns An object containing the latest subscription data or an error.
 */
export async function getLatestSubscription(supabase: SupabaseClient, userId: string) {
  try {
    const { data, error } = await supabase
      .from(TABLES.PAYMENT_SUBSCRIPTIONS)
      .select(COLUMNS.PLAN_ID)
      .eq(COLUMNS.BUSINESS_PROFILE_ID, userId)
      .order(COLUMNS.CREATED_AT, { ascending: false })
      .limit(1)
      .maybeSingle();

    if (error) {
      console.error(`Error fetching latest subscription: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error(`Unexpected error fetching latest subscription: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Fetches the custom_ads data for a business profile.
 * @param userId The ID of the business profile.
 * @returns An object containing the custom_ads data or an error.
 */
export async function getBusinessProfileCustomAds(supabase: SupabaseClient, userId: string) {
  try {
    const { data, error } = await supabase
      .from(TABLES.BUSINESS_PROFILES)
      .select(COLUMNS.CUSTOM_ADS)
      .eq(COLUMNS.ID, userId)
      .single();

    if (error) {
      console.error(`Error fetching business profile custom ads: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data: data.custom_ads, error: null };
  } catch (err) {
    console.error(`Unexpected error fetching business profile custom ads: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Updates a business profile.
 * @param userId The ID of the business profile to update.
 * @param updates The data to update.
 * @returns An object containing the updated data or an error.
 */
export async function updateBusinessProfile(supabase: SupabaseClient, userId: string, updates: TablesUpdate<'business_profiles'>) {
  try {
    const { data, error } = await supabase
      .from(TABLES.BUSINESS_PROFILES)
      .update(updates)
      .eq(COLUMNS.ID, userId)
      .select()
      .single();

    if (error) {
      console.error(`Error updating business profile: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error(`Unexpected error updating business profile: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Fetches the custom_branding data for a business profile.
 * @param userId The ID of the business profile.
 * @returns An object containing the custom_branding data or an error.
 */
export async function getBusinessProfileCustomBranding(supabase: SupabaseClient, userId: string) {
  try {
    const { data, error } = await supabase
      .from(TABLES.BUSINESS_PROFILES)
      .select(COLUMNS.CUSTOM_BRANDING)
      .eq(COLUMNS.ID, userId)
      .single();

    if (error) {
      console.error(`Error fetching business profile custom branding: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data: data.custom_branding as Tables<'business_profiles'>['custom_branding'], error: null };
  } catch (err) {
    console.error(`Unexpected error fetching business profile custom branding: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Fetches the phone number from a business profile.
 * @param userId The ID of the business profile.
 * @returns An object containing the phone number or an error.
 */
export async function getBusinessProfilePhone(supabase: SupabaseClient, userId: string) {
  try {
    const { data, error } = await supabase
      .from(TABLES.BUSINESS_PROFILES)
      .select(COLUMNS.PHONE)
      .eq(COLUMNS.ID, userId)
      .single();

    if (error) {
      console.error(`Error fetching business profile phone: ${error.message}`);
      return { phone: null, error: error.message };
    }
    return { phone: data.phone, error: null };
  } catch (err) {
    console.error(`Unexpected error fetching business profile phone: ${err}`);
    return { phone: null, error: "An unexpected error occurred." };
  }
}

/**
 * Fetches a business profile with all relevant details for the business card.
 * @param userId The ID of the user to fetch the profile for.
 * @returns An object containing the business profile data or an error.
 */
export async function getBusinessProfileWithAllDetails(supabase: SupabaseClient, userId: string) {
  try {
    const { data, error } = await supabase
      .from(TABLES.BUSINESS_PROFILES)
      .select(
        `
        ${COLUMNS.ID}, ${COLUMNS.BUSINESS_NAME}, ${COLUMNS.CONTACT_EMAIL}, ${COLUMNS.HAS_ACTIVE_SUBSCRIPTION},
        ${COLUMNS.TRIAL_END_DATE}, ${COLUMNS.CREATED_AT}, ${COLUMNS.UPDATED_AT}, ${COLUMNS.LOGO_URL}, ${COLUMNS.MEMBER_NAME}, ${COLUMNS.TITLE},
        ${COLUMNS.ADDRESS_LINE}, ${COLUMNS.CITY}, ${COLUMNS.STATE}, ${COLUMNS.PINCODE}, ${COLUMNS.LOCALITY}, ${COLUMNS.PHONE}, ${COLUMNS.INSTAGRAM_URL},
        ${COLUMNS.FACEBOOK_URL}, ${COLUMNS.WHATSAPP_NUMBER}, ${COLUMNS.ABOUT_BIO}, ${COLUMNS.STATUS}, ${COLUMNS.BUSINESS_SLUG},
        ${COLUMNS.TOTAL_LIKES}, ${COLUMNS.TOTAL_SUBSCRIPTIONS}, ${COLUMNS.AVERAGE_RATING}, ${COLUMNS.THEME_COLOR}, ${COLUMNS.DELIVERY_INFO}, ${COLUMNS.BUSINESS_HOURS},
        ${COLUMNS.BUSINESS_CATEGORY}, ${COLUMNS.CUSTOM_BRANDING}, ${COLUMNS.CUSTOM_ADS}, ${COLUMNS.ESTABLISHED_YEAR}
      `
      )
      .eq(COLUMNS.ID, userId)
      .single();

    if (error) {
      console.error(`Error fetching business profile with all details: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error(`Unexpected error fetching business profile with all details: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Fetches the latest subscription status for a given user ID.
 * @param userId The ID of the user to fetch the subscription status for.
 * @returns An object containing the subscription status (plan_id and subscription_status) or an error.
 */
export async function getLatestSubscriptionStatus(supabase: SupabaseClient, userId: string): Promise<{ subscriptionStatus: { plan_id: string | null; subscription_status: string | null } | null; error: string | null }> {
  try {
    const { data: subscription, error } = await supabase
      .from(TABLES.PAYMENT_SUBSCRIPTIONS)
      .select(`${COLUMNS.PLAN_ID}, ${COLUMNS.SUBSCRIPTION_STATUS}`)
      .eq(COLUMNS.BUSINESS_PROFILE_ID, userId)
      .order(COLUMNS.CREATED_AT, { ascending: false })
      .limit(1)
      .maybeSingle();

    if (error) {
      console.error(`Error fetching latest subscription status: ${error.message}`);
      return { subscriptionStatus: null, error: error.message };
    }

    return { subscriptionStatus: subscription, error: null };
  } catch (err) {
    console.error(`Unexpected error fetching latest subscription status: ${err}`);
    return { subscriptionStatus: null, error: "An unexpected error occurred." };
  }
}

/**
 * Securely fetches a business profile by slug, including subscription data.
 * This function uses the service role key to bypass RLS.
 * @param slug The slug of the business profile to fetch.
 * @returns An object containing the business profile data or an error.
 */
export async function getSecureBusinessProfileBySlug(supabase: SupabaseClient, slug: string) {
  try {
    const { data: profileData, error: profileError } = await supabase
      .from(TABLES.BUSINESS_PROFILES)
      .select(
        `
        *,
        ${TABLES.PAYMENT_SUBSCRIPTIONS}!${COLUMNS.BUSINESS_PROFILE_ID} (
          ${COLUMNS.PLAN_ID},
          ${COLUMNS.SUBSCRIPTION_STATUS}
        )
      `
      )
      .eq(COLUMNS.BUSINESS_SLUG, slug)
      .maybeSingle();

    if (profileError) {
      console.error(`Error fetching secure business profile by slug: ${profileError.message}`);
      return { data: null, error: profileError.message };
    }

    if (!profileData) {
      return { data: null, error: "Profile not found." };
    }

    const safeData = {
      ...profileData,
      subscription_status:
        (profileData[TABLES.PAYMENT_SUBSCRIPTIONS] as Tables<'payment_subscriptions'> | undefined)?.subscription_status || null,
      plan_id: (profileData[TABLES.PAYMENT_SUBSCRIPTIONS] as Tables<'payment_subscriptions'> | undefined)?.plan_id || null,
    };

    return { data: safeData, error: null };
  } catch (err) {
    console.error(`Unexpected error fetching secure business profile by slug: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Securely fetches a business profile with products by slug.
 * This function uses the service role key to bypass RLS.
 * @param slug The slug of the business profile to fetch.
 * @returns An object containing the business profile data with products or an error.
 */
export async function getSecureBusinessProfileWithProductsBySlug(supabase: SupabaseClient, slug: string) {
  try {
    const { data: profileData, error: profileError } = await supabase
      .from(TABLES.BUSINESS_PROFILES)
      .select(
        `
        *,
        ${TABLES.PRODUCTS_SERVICES} (
          ${COLUMNS.ID}, ${COLUMNS.NAME}, ${COLUMNS.DESCRIPTION}, ${COLUMNS.BASE_PRICE}, ${COLUMNS.DISCOUNTED_PRICE}, ${COLUMNS.IS_AVAILABLE}, ${COLUMNS.IMAGE_URL}, ${COLUMNS.CREATED_AT}, ${COLUMNS.UPDATED_AT}, ${COLUMNS.PRODUCT_TYPE}
        )
      `
      )
      .eq(COLUMNS.BUSINESS_SLUG, slug)
      .maybeSingle();

    if (profileError) {
      console.error(`Error fetching secure business profile with products by slug: ${profileError.message}`);
      return { data: null, error: profileError.message };
    }

    if (!profileData) {
      return { data: null, error: "Profile not found." };
    }

    const safeData = {
      ...profileData,
      products_services: (profileData[TABLES.PRODUCTS_SERVICES] as Tables<'products_services'>[]) || [],
    };

    return { data: safeData, error: null };
  } catch (err) {
    console.error(`Unexpected error fetching secure business profile with products by slug: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Fetches ad data for a given pincode.
 * @param pincode The pincode to fetch ads for.
 * @returns An object containing the ad data or an error.
 */
export async function getAdDataForPincode(supabase: SupabaseClient, pincode: string) {
  try {
    // First, check if the custom_ad_targets table exists (for backward compatibility)
    const { count, error: tableCheckError } = await supabase
      .from(TABLES.CUSTOM_AD_TARGETS)
      .select("*", { count: "exact", head: true });

    if (tableCheckError) {
      console.error(`Error checking custom_ad_targets table: ${tableCheckError.message}`);
      // Fallback to old approach if table check fails
      return { adData: null, error: tableCheckError.message };
    }

    // If the table exists and migration has been applied
    if (count !== null) {
      const { data: adData, error: adError } = await supabase.rpc(
        RPC_FUNCTIONS.GET_AD_FOR_PINCODE,
        { [RPC_PARAMS.TARGET_PINCODE]: pincode }
      );

      if (adError) {
        console.error(`Error fetching ad for pincode ${pincode}: ${adError.message}`);
        return { adData: null, error: adError.message };
      }
      return { adData: adData && adData.length > 0 ? adData[0] : null, error: null };
    } else {
      // Fallback to old approach if migration hasn't been applied yet
      const { data: customAd, error: customAdError } = await supabase
        .from(TABLES.CUSTOM_ADS)
        .select(`${COLUMNS.AD_IMAGE_URL}, ${COLUMNS.AD_LINK_URL}`)
        .eq(COLUMNS.IS_ACTIVE, true)
        .or(
          `targeting_locations.eq.'"global"',targeting_locations.cs.'["${pincode}"]'`
        )
        .order(COLUMNS.CREATED_AT, { ascending: false })
        .limit(1)
        .maybeSingle();

      if (customAdError) {
        console.error(`Error fetching custom ad (fallback): ${customAdError.message}`);
        return { adData: null, error: customAdError.message };
      }
      return { adData: customAd, error: null };
    }
  } catch (err) {
    console.error(`Unexpected error fetching ad data for pincode: ${err}`);
    return { adData: null, error: "An unexpected error occurred." };
  }
}

/**
 * Fetches products for a given business ID.
 * @param businessId The ID of the business.
 * @param limit The maximum number of products to return.
 * @returns An object containing the products data or an error.
 */
export async function getProductsForBusiness(supabase: SupabaseClient, businessId: string, limit: number) {
  try {
    const { data, error, count } = await supabase
      .from(TABLES.PRODUCTS_SERVICES)
      .select("*", { count: "exact" })
      .eq(COLUMNS.BUSINESS_ID, businessId)
      .eq(COLUMNS.IS_AVAILABLE, true)
      .order(COLUMNS.CREATED_AT, { ascending: false })
      .limit(limit);

    if (error) {
      console.error(`Error fetching products for business ${businessId}: ${error.message}`);
      return { products: null, count: 0, error: error.message };
    }
    return { products: data, count: count || 0, error: null };
  } catch (err) {
    console.error(`Unexpected error fetching products for business: ${err}`);
    return { products: null, count: 0, error: "An unexpected error occurred." };
  }
}

/**
 * Fetches the count of reviews for a given business ID, excluding self-reviews.
 * @param businessId The ID of the business.
 * @returns An object containing the reviews count or an error.
 */
export async function getReviewsCountForBusiness(supabase: SupabaseClient, businessId: string) {
  try {
    const { count, error } = await supabase
      .from(TABLES.RATINGS_REVIEWS)
      .select(COLUMNS.ID, { count: "exact", head: true })
      .eq(COLUMNS.BUSINESS_PROFILE_ID, businessId)
      .neq(COLUMNS.USER_ID, businessId); // Don't count self-reviews

    if (error) {
      console.error(`Error fetching reviews count for business ${businessId}: ${error.message}`);
      return { count: 0, error: error.message };
    }
    return { count: count || 0, error: null };
  } catch (err) {
    console.error(`Unexpected error fetching reviews count for business: ${err}`);
    return { count: 0, error: "An unexpected error occurred." };
  }
}

/**
 * Fetches the gallery data for a business profile.
 * @param businessId The ID of the business profile.
 * @returns An object containing the gallery data or an error.
 */
export async function getBusinessProfileGallery(supabase: SupabaseClient, businessId: string): Promise<{ data: Json | null; error?: string }> {
  try {
    const { data: profileData, error } = await supabase
      .from(TABLES.BUSINESS_PROFILES)
      .select(COLUMNS.GALLERY)
      .eq(COLUMNS.ID, businessId)
      .single();

    if (error) {
      console.error(`Error fetching business profile gallery: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data: profileData.gallery };
  } catch (err) {
    console.error(`Unexpected error fetching business profile gallery: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Fetches the business ID and status for a given business slug.
 * @param businessSlug The slug of the business.
 * @returns An object containing the business ID and status or an error.
 */
export async function getBusinessProfileIdAndStatusBySlug(supabase: SupabaseClient, businessSlug: string) {
  try {
    const { data: business, error } = await supabase
      .from(TABLES.BUSINESS_PROFILES)
      .select(`${COLUMNS.ID}, ${COLUMNS.STATUS}`)
      .eq(COLUMNS.BUSINESS_SLUG, businessSlug)
      .single();

    if (error) {
      console.error(`Error fetching business ID and status by slug: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data: business, error: null };
  } catch (err) {
    console.error(`Unexpected error fetching business ID and status by slug: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Fetches the public subscription status for a given business profile ID.
 * @param businessProfileId The ID of the business profile.
 * @returns An object containing the plan ID or an error.
 */
export async function getPublicSubscriptionStatus(supabase: SupabaseClient, businessProfileId: string) {
  try {
    const { data: subscription, error } = await supabase
      .from(TABLES.PUBLIC_SUBSCRIPTION_STATUS)
      .select(COLUMNS.PLAN_ID)
      .eq(COLUMNS.BUSINESS_PROFILE_ID, businessProfileId)
      .order(COLUMNS.CREATED_AT, { ascending: false })
      .limit(1)
      .maybeSingle();

    if (error) {
      console.error(`Error fetching public subscription status: ${error.message}`);
      return { planId: null, error: error.message };
    }
    return { planId: subscription?.plan_id || null, error: null };
  } catch (err) {
    console.error(`Unexpected error fetching public subscription status: ${err}`);
    return { planId: null, error: "An unexpected error occurred." };
  }
}

/**
 * Fetches products for a given business ID with filters, sorting, and pagination.
 * @param businessId The ID of the business.
 * @param page The page number (1-based).
 * @param sortBy The sorting criteria.
 * @param pageSize The number of items per page.
 * @param searchTerm Optional search term for product names.
 * @param productType Optional product type filter.
 * @returns An object containing the products data, total count, or an error.
 */
export async function getProductsWithFiltersAndPagination(
  supabase: SupabaseClient,
  businessId: string,
  page: number = 1,
  sortBy: string = "created_desc",
  pageSize: number = 20,
  searchTerm?: string | null,
  productType?: string | null
) {
  const offset = (page - 1) * pageSize;

  let query = supabase
    .from(TABLES.PRODUCTS_SERVICES)
    .select(
      `
      ${COLUMNS.ID},
      ${COLUMNS.BUSINESS_ID},
      ${COLUMNS.NAME},
      ${COLUMNS.DESCRIPTION},
      ${COLUMNS.BASE_PRICE},
      ${COLUMNS.DISCOUNTED_PRICE},
      ${COLUMNS.PRODUCT_TYPE},
      ${COLUMNS.IS_AVAILABLE},
      ${COLUMNS.IMAGE_URL},
      ${COLUMNS.CREATED_AT},
      ${COLUMNS.UPDATED_AT},
      ${COLUMNS.SLUG}
    `,
      { count: "exact" }
    )
    .eq(COLUMNS.BUSINESS_ID, businessId)
    .eq(COLUMNS.IS_AVAILABLE, true);

  if (searchTerm && searchTerm.trim().length > 0) {
    query = query.ilike(COLUMNS.NAME, `%${searchTerm.trim()}%`);
  }

  if (productType && productType !== "all") {
    query = query.eq(COLUMNS.PRODUCT_TYPE, productType);
  }

  switch (sortBy) {
    case "created_asc":
      query = query.order(COLUMNS.CREATED_AT, { ascending: true });
      break;
    case "updated_desc":
      query = query.order(COLUMNS.UPDATED_AT, { ascending: false });
      break;
    case "price_asc":
      query = query
        .order(COLUMNS.DISCOUNTED_PRICE, { ascending: true, nullsFirst: false })
        .order(COLUMNS.BASE_PRICE, { ascending: true, nullsFirst: false });
      break;
    case "price_desc":
      query = query
        .order(COLUMNS.DISCOUNTED_PRICE, { ascending: false, nullsFirst: false })
        .order(COLUMNS.BASE_PRICE, { ascending: false, nullsFirst: false });
      break;
    case "name_asc":
      query = query.order(COLUMNS.NAME, { ascending: true });
      break;
    case "name_desc":
      query = query.order(COLUMNS.NAME, { ascending: false });
      break;
    case "created_desc":
    default:
      query = query.order(COLUMNS.CREATED_AT, { ascending: false });
      break;
  }

  query = query.range(offset, offset + pageSize - 1);

  const { data, error, count } = await query;

  if (error) {
    console.error(`Error fetching products: ${error.message}`);
    return { data: null, error: error.message, totalCount: 0 };
  }

  return { data: data as Tables<'products_services'>[], error: null, totalCount: count || 0 };
}

/**
 * Fetches the status of a business profile.
 * @param businessProfileId The ID of the business profile.
 * @returns An object containing the status or an error.
 */
export async function getBusinessProfileStatus(supabase: SupabaseClient, businessProfileId: string) {
  try {
    const { data: profile, error } = await supabase
      .from(TABLES.BUSINESS_PROFILES)
      .select(COLUMNS.STATUS)
      .eq(COLUMNS.ID, businessProfileId)
      .single();

    if (error) {
      console.error(`Error fetching business profile status: ${error.message}`);
      return { status: null, error: error.message };
    }
    return { status: profile.status, error: null };
  } catch (err) {
    console.error(`Unexpected error fetching business profile status: ${err}`);
    return { status: null, error: "An unexpected error occurred." };
  }
}

/**
 * Inserts a new card visit record.
 * @param visitData The data for the card visit.
 * @returns An object indicating success or an error.
 */
export async function insertCardVisit(supabase: SupabaseClient, visitData: TablesInsert<'card_visits'>) {
  try {
    const { error } = await supabase.from(TABLES.CARD_VISITS).insert([visitData]);

    if (error) {
      console.error(`Error inserting card visit: ${error.message}`);
      return { success: false, error: error.message };
    }
    return { success: true, error: null };
  } catch (err) {
    console.error(`Unexpected error inserting card visit: ${err}`);
    return { success: false, error: "An unexpected error occurred." };
  }
}

/**
 * Updates the logo URL for a business profile in the database.
 * @param userId - The ID of the user whose business profile is being updated.
 * @param logoUrl - The new logo URL to set.
 * @returns A Promise that resolves to an object indicating success or containing an error message.
 */
export async function updateBusinessLogoUrl(
  supabase: SupabaseClient,
  userId: string,
  logoUrl: string | null
): Promise<{ success: boolean; error?: string }> {
  const { error: updateError } = await supabase
    .from(TABLES.BUSINESS_PROFILES)
    .update({ [COLUMNS.LOGO_URL]: logoUrl as string | null | undefined, [COLUMNS.UPDATED_AT]: new Date().toISOString() })
    .eq(COLUMNS.ID, userId);

  if (updateError) {
    console.error("Update Business Logo URL Error:", updateError);
    return {
      success: false,
      error: `Failed to update business logo URL: ${updateError.message}`,
    };
  }
  return { success: true };
}

/**
 * Fetches the current logo URL for a business profile.
 * @param userId - The ID of the user whose business profile logo URL is being fetched.
 * @returns A Promise that resolves to an object containing the logo URL or an error message.
 */
export async function getBusinessLogoUrl(
  supabase: SupabaseClient,
  userId: string
): Promise<{ logoUrl: string | null; error?: string }> {
  const { data, error: fetchError } = await supabase
    .from(TABLES.BUSINESS_PROFILES)
    .select(COLUMNS.LOGO_URL)
    .eq(COLUMNS.ID, userId)
    .single();

  if (fetchError) {
    console.error("Error fetching business logo URL:", fetchError);
    return { logoUrl: null, error: "Failed to fetch business logo URL." };
  }

  return { logoUrl: data?.logo_url || null };
}

/**
 * Checks if a business slug is unique and not currently in use by another business profile.
 * @param slug The business slug to check for availability.
 * @param excludeUserId Optional. A user ID to exclude from the check (e.g., the current user's profile).
 * @returns An object indicating whether the slug is available and an error message if any.
 */
export async function checkBusinessSlugUniqueness(supabase: SupabaseClient, slug: string, excludeUserId: string | null = null) {
  try {
    let query = supabase
      .from(TABLES.BUSINESS_PROFILES)
      .select(COLUMNS.ID)
      .ilike(COLUMNS.BUSINESS_SLUG, slug);

    if (excludeUserId) {
      query = query.neq(COLUMNS.ID, excludeUserId);
    }

    const { data, error } = await query.maybeSingle();

    if (error) {
      console.error(`Error checking business slug uniqueness: ${error.message}`);
      return { available: false, error: error.message };
    }

    return { available: !data, error: null };
  } catch (err) {
    console.error(`Unexpected error checking business slug uniqueness: ${err}`);
    return { available: false, error: "An unexpected error occurred." };
  }
}

/**
 * Fetches business profiles based on city, status, category, with pagination and sorting.
 * @param city The city to filter by.
 * @param status The status of the business profile (e.g., 'online').
 * @param category Optional. The business category to filter by.
 * @param page The page number for pagination (1-based).
 * @param limit The number of results per page.
 * @param sortBy The column to sort by.
 * @param ascending Whether to sort in ascending order.
 * @returns An object containing the business profiles data and count, or an error.
 */
export async function getBusinessProfilesByCity(
  supabase: SupabaseClient,
  city: string,
  status: string,
  category: string | null,
  page: number,
  limit: number,
  sortBy: string,
  ascending: boolean
): Promise<{ data: any[] | null; count: number | null; error: string | null }> {
  try {
    const offset = (page - 1) * limit;

    let query = supabase
      .from(TABLES.BUSINESS_PROFILES)
      .select(
        `
        ${COLUMNS.ID}, ${COLUMNS.BUSINESS_NAME}, ${COLUMNS.LOGO_URL}, ${COLUMNS.MEMBER_NAME}, ${COLUMNS.TITLE},
        ${COLUMNS.ADDRESS_LINE}, ${COLUMNS.CITY}, ${COLUMNS.STATE}, ${COLUMNS.PINCODE}, ${COLUMNS.LOCALITY}, ${COLUMNS.PHONE}, ${COLUMNS.BUSINESS_CATEGORY}, ${COLUMNS.INSTAGRAM_URL},
        ${COLUMNS.FACEBOOK_URL}, ${COLUMNS.WHATSAPP_NUMBER}, ${COLUMNS.ABOUT_BIO}, ${COLUMNS.STATUS}, ${COLUMNS.BUSINESS_SLUG}, ${COLUMNS.THEME_COLOR},
        ${COLUMNS.DELIVERY_INFO}, ${COLUMNS.TOTAL_LIKES}, ${COLUMNS.TOTAL_SUBSCRIPTIONS}, ${COLUMNS.AVERAGE_RATING}, ${COLUMNS.BUSINESS_HOURS},
        ${COLUMNS.TRIAL_END_DATE}, ${COLUMNS.CREATED_AT}, ${COLUMNS.UPDATED_AT}, ${COLUMNS.CONTACT_EMAIL}, ${COLUMNS.ESTABLISHED_YEAR}
        `,
        { count: "exact" }
      )
      .eq(COLUMNS.CITY, city)
      .eq(COLUMNS.STATUS, status);

    if (category && category.trim()) {
      query = query.eq(COLUMNS.BUSINESS_CATEGORY, category.trim());
    }

    const { data, count, error } = await query
      .range(offset, offset + limit - 1)
      .order(sortBy, { ascending });

    if (error) {
      console.error(`Error fetching business profiles by city: ${error.message}`);
      return { data: null, count: null, error: error.message };
    }

    return { data, count, error: null };
  } catch (err) {
    console.error(`Unexpected error fetching business profiles by city: ${err}`);
    return { data: null, count: null, error: "An unexpected error occurred." };
  }
}

/**
 * Fetches business IDs based on city, status, and category.
 * @param city The city to filter by.
 * @param status The status of the business profile (e.g., 'online').
 * @param category Optional. The business category to filter by.
 * @returns An object containing an array of business IDs or an error.
 */
export async function getBusinessIdsByCity(
  supabase: SupabaseClient,
  city: string,
  status: string,
  category: string | null
): Promise<{ data: string[] | null; error: string | null }> {
  try {
    let query = supabase
      .from(TABLES.BUSINESS_PROFILES)
      .select(COLUMNS.ID)
      .eq(COLUMNS.CITY, city)
      .eq(COLUMNS.STATUS, status);

    if (category && category.trim()) {
      query = query.eq(COLUMNS.BUSINESS_CATEGORY, category.trim());
    }

    const { data, error } = await query;

    if (error) {
      console.error(`Error fetching business IDs by city: ${error.message}`);
      return { data: null, error: error.message };
    }

    return { data: data.map((item) => item.id), error: null };
  } catch (err) {
    console.error(`Unexpected error fetching business IDs by city: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Gets the count of products for a given set of business IDs.
 * @param businessIds An array of business IDs.
 * @param productType Optional. The product type to filter by.
 * @returns An object containing the product count or an error.
 */
export async function getProductCountByBusinessIds(
  supabase: SupabaseClient,
  businessIds: string[],
  productType: string | null
): Promise<{ count: number | null; error: string | null }> {
  try {
    let query = supabase
      .from(TABLES.PRODUCTS_SERVICES)
      .select(COLUMNS.ID, { count: "exact" })
      .in(COLUMNS.BUSINESS_ID, businessIds)
      .eq(COLUMNS.IS_AVAILABLE, true);

    if (productType) {
      query = query.eq(COLUMNS.PRODUCT_TYPE, productType);
    }

    const { count, error } = await query;

    if (error) {
      console.error(`Error counting products by business IDs: ${error.message}`);
      return { count: null, error: error.message };
    }

    return { count, error: null };
  } catch (err) {
    console.error(`Unexpected error counting products by business IDs: ${err}`);
    return { count: null, error: "An unexpected error occurred." };
  }
}

/**
 * Fetches products for a given set of business IDs with pagination and sorting.
 * @param businessIds An array of business IDs.
 * @param page The page number for pagination (1-based).
 * @param limit The number of results per page.
 * @param sortBy The column to sort by.
 * @param ascending Whether to sort in ascending order.
 * @param productType Optional. The product type to filter by.
 * @returns An object containing the products data or an error.
 */
export async function getProductsByBusinessIds(
  supabase: SupabaseClient,
  businessIds: string[],
  page: number,
  limit: number,
  sortBy: string,
  ascending: boolean,
  productType: string | null
): Promise<{ data: any[] | null; error: string | null }> {
  try {
    const from = (page - 1) * limit;
    const to = from + limit - 1;

    let query = supabase
      .from(TABLES.PRODUCTS_SERVICES)
      .select(
        `
        ${COLUMNS.ID}, ${COLUMNS.BUSINESS_ID}, ${COLUMNS.NAME}, ${COLUMNS.DESCRIPTION}, ${COLUMNS.BASE_PRICE}, ${COLUMNS.DISCOUNTED_PRICE}, ${COLUMNS.PRODUCT_TYPE},
        ${COLUMNS.IS_AVAILABLE}, ${COLUMNS.IMAGE_URL}, ${COLUMNS.CREATED_AT}, ${COLUMNS.UPDATED_AT}, ${COLUMNS.SLUG},
        business_profiles!${COLUMNS.BUSINESS_ID}(${COLUMNS.BUSINESS_SLUG})
        `
      )
      .in(COLUMNS.BUSINESS_ID, businessIds)
      .eq(COLUMNS.IS_AVAILABLE, true);

    if (productType) {
      query = query.eq(COLUMNS.PRODUCT_TYPE, productType);
    }

    // Apply sorting based on the sortBy parameter
    if (sortBy === COLUMNS.DISCOUNTED_PRICE) {
      query = query
        .order(COLUMNS.DISCOUNTED_PRICE, { ascending, nullsFirst: false })
        .order(COLUMNS.BASE_PRICE, { ascending, nullsFirst: false });
    } else {
      query = query.order(sortBy, { ascending });
    }

    const { data, error } = await query.range(from, to);

    if (error) {
      console.error(`Error fetching products by business IDs: ${error.message}`);
      return { data: null, error: error.message };
    }

    return { data, error: null };
  } catch (err) {
    console.error(`Unexpected error fetching products by business IDs: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Fetches a product by its ID and business ID.
 * @param productId The ID of the product.
 * @param businessId The ID of the business.
 * @returns An object containing the product data or an error.
 */
export async function getProductByIdAndBusinessId(supabase: SupabaseClient, productId: string, businessId: string) {
  try {
    const { data, error } = await supabase
      .from(TABLES.PRODUCTS_SERVICES)
      .select(`${COLUMNS.ID}, ${COLUMNS.BUSINESS_ID}`)
      .eq(COLUMNS.ID, productId)
      .eq(COLUMNS.BUSINESS_ID, businessId)
      .single();

    if (error) {
      console.error(`Error fetching product by ID and business ID: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error(`Unexpected error fetching product by ID and business ID: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Fetches existing product variants for a given product ID.
 * @param productId The ID of the product.
 * @returns An object containing the existing product variants or an error.
 */
export async function getExistingProductVariants(supabase: SupabaseClient, productId: string) {
  try {
    const { data, error } = await supabase
      .from(TABLES.PRODUCT_VARIANTS)
      .select(`${COLUMNS.ID}, ${COLUMNS.VARIANT_VALUES}`)
      .eq(COLUMNS.PRODUCT_ID, productId);

    if (error) {
      console.error(`Error fetching existing product variants: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error(`Unexpected error fetching existing product variants: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Inserts a new product variant.
 * @param variantData The data for the new product variant.
 * @returns An object containing the newly inserted product variant data or an error.
 */
export async function insertProductVariant(supabase: SupabaseClient, variantData: TablesInsert<'product_variants'>) {
  try {
    const { data, error } = await supabase
      .from(TABLES.PRODUCT_VARIANTS)
      .insert(variantData)
      .select()
      .single();

    if (error) {
      console.error(`Error inserting product variant: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error(`Unexpected error inserting product variant: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Deletes a product variant by its ID.
 * @param variantId The ID of the product variant to delete.
 * @returns An object indicating success or an error.
 */
export async function deleteProductVariant(supabase: SupabaseClient, variantId: string) {
  try {
    const { error } = await supabase
      .from(TABLES.PRODUCT_VARIANTS)
      .delete()
      .eq(COLUMNS.ID, variantId);

    if (error) {
      console.error(`Error deleting product variant: ${error.message}`);
      return { success: false, error: error.message };
    }
    return { success: true, error: null };
  } catch (err) {
    console.error(`Unexpected error deleting product variant: ${err}`);
    return { success: false, error: "An unexpected error occurred." };
  }
}

/**
 * Updates a product variant with new image URLs.
 * @param variantId The ID of the product variant to update.
 * @param imageUrls An array of new image URLs.
 * @param featuredImageIndex The index of the featured image.
 * @returns An object indicating success or an error.
 */
export async function updateProductVariantImages(supabase: SupabaseClient, variantId: string, imageUrls: string[], featuredImageIndex: number) {
  try {
    const updateData = {
      images: imageUrls,
      featured_image_index: featuredImageIndex,
    };
    const { error } = await supabase
      .from(TABLES.PRODUCT_VARIANTS)
      .update(updateData)
      .eq(COLUMNS.ID, variantId);

    if (error) {
      console.error(`Error updating product variant images: ${error.message}`);
      return { success: false, error: error.message };
    }
    return { success: true, error: null };
  } catch (err) {
    console.error(`Unexpected error updating product variant images: ${err}`);
    return { success: false, error: "An unexpected error occurred." };
  }
}

/**
 * Inserts multiple product variants in a single operation.
 * @param variantDataArray An array of product variant data to insert.
 * @returns An object containing the newly inserted product variant data or an error.
 */
export async function insertMultipleProductVariants(supabase: SupabaseClient, variantDataArray: TablesInsert<'product_variants'>[]) {
  try {
    const { data, error } = await supabase
      .from(TABLES.PRODUCT_VARIANTS)
      .insert(variantDataArray)
      .select();

    if (error) {
      console.error(`Error inserting multiple product variants: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error(`Unexpected error inserting multiple product variants: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Fetches the locality, pincode, and city slug for a business profile.
 * @param userId The ID of the user.
 * @returns An object containing the location data or an error.
 */
export async function getBusinessProfileLocation(supabase: SupabaseClient, userId: string) {
  try {
    const { data, error } = await supabase
      .from(TABLES.BUSINESS_PROFILES)
      .select(`${COLUMNS.LOCALITY_SLUG}, ${COLUMNS.PINCODE}, ${COLUMNS.CITY_SLUG}, ${COLUMNS.STATE_SLUG}`)
      .eq(COLUMNS.ID, userId)
      .single();

    if (error) {
      console.error(`Error fetching business profile location: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error(`Unexpected error fetching business profile location: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Fetches the total count of likes for a business.
 * @param businessId The ID of the business.
 * @returns The total count of likes.
 */
export async function getBusinessLikesCount(supabase: SupabaseClient, businessId: string): Promise<number> {
  const { count, error } = await supabase
    .from(TABLES.LIKES)
    .select(COLUMNS.ID, { count: 'exact', head: true })
    .eq(COLUMNS.BUSINESS_PROFILE_ID, businessId);

  if (error) {
    console.error('Error fetching business likes count:', error);
    throw new Error('Failed to get total count');
  }

  return count || 0;
}

/**
 * Fetches likes for a business with pagination.
 * @param businessId The ID of the business.
 * @param page The page number.
 * @param limit The number of items per page.
 * @returns A list of likes.
 */
export async function getBusinessLikes(supabase: SupabaseClient, businessId: string, page: number, limit: number) {
  const from = (page - 1) * limit;
  const { data, error } = await supabase
    .from(TABLES.LIKES)
    .select(`${COLUMNS.ID}, ${COLUMNS.USER_ID}`)
    .eq(COLUMNS.BUSINESS_PROFILE_ID, businessId)
    .range(from, from + limit - 1);

  if (error) {
    console.error('Error fetching business likes:', error);
    throw new Error('Failed to fetch likes');
  }

  return data;
}

/**
 * Fetches the total count of businesses liked by a business.
 * @param businessId The ID of the business.
 * @param searchTerm Optional search term for business names.
 * @returns The total count of liked businesses.
 */
export async function getMyLikesCount(supabase: SupabaseClient, businessId: string, searchTerm?: string): Promise<number> {
  let countQuery = supabase
    .from(TABLES.LIKES)
    .select(`
      ${COLUMNS.ID},
      ${TABLES.BUSINESS_PROFILES}!inner (
        ${COLUMNS.ID},
        ${COLUMNS.BUSINESS_NAME}
      )
    `, { count: 'exact', head: true })
    .eq(COLUMNS.USER_ID, businessId);

  if (searchTerm) {
    countQuery = countQuery.ilike(`${TABLES.BUSINESS_PROFILES}.${COLUMNS.BUSINESS_NAME}`, `%${searchTerm}%`);
  }

  const { count, error } = await countQuery;

  if (error) {
    console.error('Error fetching my likes count:', error);
    throw new Error('Failed to get total count');
  }

  return count || 0;
}

/**
 * Fetches businesses liked by a business with pagination and search.
 * @param businessId The ID of the business.
 * @param page The page number.
 * @param limit The number of items per page.
 * @param searchTerm Optional search term for business names.
 * @returns A list of liked businesses.
 */
export async function getMyLikes(supabase: SupabaseClient, businessId: string, page: number, limit: number, searchTerm?: string) {
  const from = (page - 1) * limit;
  let query = supabase
    .from(TABLES.LIKES)
    .select(`
      ${COLUMNS.ID},
      ${TABLES.BUSINESS_PROFILES}!inner (
        ${COLUMNS.ID},
        ${COLUMNS.BUSINESS_NAME},
        ${COLUMNS.BUSINESS_SLUG},
        ${COLUMNS.LOGO_URL},
        ${COLUMNS.CITY},
        ${COLUMNS.STATE},
        ${COLUMNS.PINCODE},
        ${COLUMNS.ADDRESS_LINE},
        ${COLUMNS.LOCALITY}
      )
    `)
    .eq(COLUMNS.USER_ID, businessId);

  if (searchTerm) {
    query = query.ilike(`${TABLES.BUSINESS_PROFILES}.${COLUMNS.BUSINESS_NAME}`, `%${searchTerm}%`);
  }

  const { data, error } = await query.range(from, from + limit - 1);

  if (error) {
    console.error('Error fetching my likes:', error);
    throw new Error('Failed to fetch likes');
  }

  return data;
}

/**
 * Fetches business profiles by their IDs.
 * @param userIds An array of user IDs.
 * @returns An object containing the business profiles data or an error.
 */
export async function getBusinessProfilesByIds(supabase: SupabaseClient, userIds: string[]) {
  try {
    const { data, error } = await supabase
      .from(TABLES.BUSINESS_PROFILES)
      .select(`${COLUMNS.ID}, ${COLUMNS.BUSINESS_NAME}, ${COLUMNS.BUSINESS_SLUG}, ${COLUMNS.LOGO_URL}, ${COLUMNS.CITY}, ${COLUMNS.STATE}, ${COLUMNS.PINCODE}, ${COLUMNS.ADDRESS_LINE}`)
      .in(COLUMNS.ID, userIds);

    if (error) {
      console.error(`Error fetching business profiles by IDs: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error(`Unexpected error fetching business profiles by IDs: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Inserts a new product into the database.
 * @param productData The data for the new product.
 * @returns An object containing the newly inserted product data or an error.
 */
export async function insertProduct(supabase: SupabaseClient, productData: TablesInsert<'products_services'>) {
  try {
    const { data, error } = await supabase
      .from(TABLES.PRODUCTS_SERVICES)
      .insert(productData)
      .select(`${COLUMNS.ID}, ${COLUMNS.PRODUCT_TYPE}, ${COLUMNS.NAME}, ${COLUMNS.DESCRIPTION}, ${COLUMNS.BASE_PRICE}, ${COLUMNS.DISCOUNTED_PRICE}, ${COLUMNS.IS_AVAILABLE}, ${COLUMNS.IMAGE_URL}, ${COLUMNS.IMAGES}, ${COLUMNS.FEATURED_IMAGE_INDEX}, ${COLUMNS.CREATED_AT}, ${COLUMNS.UPDATED_AT}, ${COLUMNS.SLUG}`)
      .single();

    if (error) {
      console.error(`Error inserting product: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error(`Unexpected error inserting product: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Updates an existing product in the database.
 * @param productId The ID of the product to update.
 * @param updates The data to update.
 * @returns An object containing the updated product data or an error.
 */
export async function updateProduct(supabase: SupabaseClient, productId: string, updates: TablesUpdate<'products_services'>) {
  try {
    const { data, error } = await supabase
      .from(TABLES.PRODUCTS_SERVICES)
      .update(updates)
      .eq(COLUMNS.ID, productId)
      .select(`${COLUMNS.ID}, ${COLUMNS.IMAGE_URL}, ${COLUMNS.IMAGES}, ${COLUMNS.FEATURED_IMAGE_INDEX}`)
      .single();

    if (error) {
      console.error(`Error updating product: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error(`Unexpected error updating product: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Fetches a product by its ID.
 * @param productId The ID of the product to fetch.
 * @returns An object containing the product data or an error.
 */
export async function getProductById(supabase: SupabaseClient, productId: string) {
  try {
    const { data, error } = await supabase
      .from(TABLES.PRODUCTS_SERVICES)
      .select(`${COLUMNS.ID}, ${COLUMNS.PRODUCT_TYPE}, ${COLUMNS.NAME}, ${COLUMNS.DESCRIPTION}, ${COLUMNS.BASE_PRICE}, ${COLUMNS.DISCOUNTED_PRICE}, ${COLUMNS.IS_AVAILABLE}, ${COLUMNS.IMAGE_URL}, ${COLUMNS.IMAGES}, ${COLUMNS.FEATURED_IMAGE_INDEX}, ${COLUMNS.CREATED_AT}, ${COLUMNS.UPDATED_AT}, ${COLUMNS.SLUG}`)
      .eq(COLUMNS.ID, productId)
      .single();

    if (error) {
      console.error(`Error fetching product by ID: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error(`Unexpected error fetching product by ID: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Fetches product variants along with their associated product's business ID.
 * @param variantIds An array of variant IDs.
 * @returns An object containing the variant data or an error.
 */
export async function getVariantsWithProductBusinessId(supabase: SupabaseClient, variantIds: string[]) {
  try {
    const { data, error } = await supabase
      .from(TABLES.PRODUCT_VARIANTS)
      .select(`
        ${COLUMNS.ID},
        ${COLUMNS.PRODUCT_ID},
        ${COLUMNS.BASE_PRICE},
        ${COLUMNS.DISCOUNTED_PRICE},
        ${COLUMNS.IS_AVAILABLE},
        ${TABLES.PRODUCTS_SERVICES}!inner(${COLUMNS.BUSINESS_ID})
      `)
      .in(COLUMNS.ID, variantIds);

    if (error) {
      console.error(`Error fetching variants with product business ID: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error(`Unexpected error fetching variants with product business ID: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Fetches a business profile by its ID.
 * @param businessId The ID of the business profile.
 * @returns An object containing the business profile data or an error.
 */
export async function getBusinessProfileById(supabase: SupabaseClient, businessId: string) {
  try {
    const { data, error } = await supabase
      .from(TABLES.BUSINESS_PROFILES)
      .select(`${COLUMNS.ID}, ${COLUMNS.BUSINESS_NAME}, ${COLUMNS.LOGO_URL}`)
      .eq(COLUMNS.ID, businessId)
      .single();

    if (error) {
      console.error(`Error fetching business profile by ID: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error(`Unexpected error fetching business profile by ID: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Performs a bulk update on product variants.
 * @param variantIds An array of variant IDs to update.
 * @param updateData The data to update.
 * @returns An object containing the updated variant data or an error.
 */
export async function bulkUpdateProductVariants(supabase: SupabaseClient, variantIds: string[], updateData: TablesUpdate<'product_variants'>) {
  try {
    const { data, error } = await supabase
      .from(TABLES.PRODUCT_VARIANTS)
      .update(updateData)
      .in(COLUMNS.ID, variantIds)
      .select(COLUMNS.ID);

    if (error) {
      console.error(`Error bulk updating product variants: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error(`Unexpected error bulk updating product variants: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Updates a single product variant.
 * @param variantId The ID of the variant to update.
 * @param updateData The data to update.
 * @returns An object indicating success or an error.
 */
export async function updateProductVariant(supabase: SupabaseClient, variantId: string, updateData: TablesUpdate<'product_variants'>) {
  try {
    const { error } = await supabase
      .from(TABLES.PRODUCT_VARIANTS)
      .update(updateData)
      .eq(COLUMNS.ID, variantId);

    if (error) {
      console.error(`Error updating product variant: ${error.message}`);
      return { success: false, error: error.message };
    }
    return { success: true, error: null };
  } catch (err) {
    console.error(`Unexpected error updating product variant: ${err}`);
    return { success: false, error: "An unexpected error occurred." };
  }
}

/**
 * Fetches product details by ID and business ID.
 * @param productId The ID of the product.
 * @param businessId The ID of the business.
 * @returns An object containing the product data or an error.
 */
export async function getProductDetailsByIdAndBusinessId(supabase: SupabaseClient, productId: string, businessId: string) {
  try {
    const { data, error } = await supabase
      .from(TABLES.PRODUCTS_SERVICES)
      .select(`${COLUMNS.IMAGES}, ${COLUMNS.NAME}`)
      .eq(COLUMNS.ID, productId)
      .eq(COLUMNS.BUSINESS_ID, businessId)
      .single();

    if (error) {
      console.error(`Error fetching product details by ID and business ID: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error(`Unexpected error fetching product details by ID and business ID: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Deletes a product by its ID and business ID.
 * @param productId The ID of the product to delete.
 * @param businessId The ID of the business.
 * @returns An object indicating success or an error.
 */
export async function deleteProductByIdAndBusinessId(supabase: SupabaseClient, productId: string, businessId: string) {
  try {
    const { error } = await supabase
      .from(TABLES.PRODUCTS_SERVICES)
      .delete()
      .eq(COLUMNS.ID, productId)
      .eq(COLUMNS.BUSINESS_ID, businessId);

    if (error) {
      console.error(`Error deleting product by ID and business ID: ${error.message}`);
      return { success: false, error: error.message };
    }
    return { success: true, error: null };
  } catch (err) {
    console.error(`Unexpected error deleting product by ID and business ID: ${err}`);
    return { success: false, error: "An unexpected error occurred." };
  }
}

/**
 * Deletes all product variants for a given product ID.
 * @param productId The ID of the product.
 * @returns An object indicating success or an error.
 */
export async function deleteProductVariantsByProductId(supabase: SupabaseClient, productId: string) {
  try {
    const { error } = await supabase
      .from(TABLES.PRODUCT_VARIANTS)
      .delete()
      .eq(COLUMNS.PRODUCT_ID, productId);

    if (error) {
      console.error(`Error deleting product variants by product ID: ${error.message}`);
      return { success: false, error: error.message };
    }
    return { success: true, error: null };
  } catch (err) {
    console.error(`Unexpected error deleting product variants by product ID: ${err}`);
    return { success: false, error: "An unexpected error occurred." };
  }
}

/**
 * Calls the 'get_product_with_variants' RPC function.
 * @param productId The ID of the product.
 * @returns An object containing the product data with variants or an error.
 */
export async function getRpcProductWithVariants(supabase: SupabaseClient, productId: string) {
  try {
    const { data, error } = await supabase
      .rpc(RPC_FUNCTIONS.GET_PRODUCT_WITH_VARIANTS, { product_uuid: productId });

    if (error) {
      console.error(`Error calling get_product_with_variants: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error(`Unexpected error in getRpcProductWithVariants: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Fetches the business ID of a product.
 * @param productId The ID of the product.
 * @param userId The ID of the user.
 * @returns An object containing the product data or an error.
 */
export async function getProductBusinessId(supabase: SupabaseClient, productId: string, userId: string) {
  try {
    const { data, error } = await supabase
      .from(TABLES.PRODUCTS_SERVICES)
      .select(COLUMNS.BUSINESS_ID)
      .eq(COLUMNS.ID, productId)
      .eq(COLUMNS.BUSINESS_ID, userId)
      .single();

    if (error) {
      console.error(`Error fetching product business ID: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error(`Unexpected error fetching product business ID: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Calls the 'get_available_product_variants' RPC function.
 * @param productId The ID of the product.
 * @returns An object containing the available product variants or an error.
 */
export async function getRpcAvailableProductVariants(supabase: SupabaseClient, productId: string) {
  try {
    const { data, error } = await supabase
      .rpc(RPC_FUNCTIONS.GET_AVAILABLE_PRODUCT_VARIANTS, { product_uuid: productId });

    if (error) {
      console.error(`Error calling get_available_product_variants: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error(`Unexpected error in getRpcAvailableProductVariants: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Fetches product variants with filtering and sorting.
 * @param productId The ID of the product.
 * @param options Filtering and sorting options.
 * @returns An object containing the product variants data and count, or an error.
 */
export async function getFilteredProductVariants(
  supabase: SupabaseClient,
  productId: string,
  options: {
    includeUnavailable?: boolean;
    sortBy?: "created_asc" | "created_desc" | "name_asc" | "name_desc" | "price_asc" | "price_desc";
    limit?: number;
    offset?: number;
  } = {}
) {
  try {
    let query = supabase
      .from(TABLES.PRODUCT_VARIANTS)
      .select("*", { count: "exact" })
      .eq(COLUMNS.PRODUCT_ID, productId);

    if (!options.includeUnavailable) {
      query = query.eq(COLUMNS.IS_AVAILABLE, true);
    }

    switch (options.sortBy) {
      case "created_asc":
        query = query.order(COLUMNS.CREATED_AT, { ascending: true });
        break;
      case "created_desc":
        query = query.order(COLUMNS.CREATED_AT, { ascending: false });
        break;
      case "name_asc":
        query = query.order(COLUMNS.VARIANT_NAME, { ascending: true });
        break;
      case "name_desc":
        query = query.order(COLUMNS.VARIANT_NAME, { ascending: false });
        break;
      case "price_asc":
        query = query.order(COLUMNS.BASE_PRICE, { ascending: true, nullsFirst: false });
        break;
      case "price_desc":
        query = query.order(COLUMNS.BASE_PRICE, { ascending: false, nullsFirst: true });
        break;
      default:
        query = query.order(COLUMNS.CREATED_AT, { ascending: false });
    }

    if (options.limit) {
      query = query.limit(options.limit);
    }
    if (options.offset) {
      query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
    }

    const { data, error: queryError, count } = await query;

    if (queryError) {
      console.error(`Error fetching filtered product variants: ${queryError.message}`);
      return { data: null, error: queryError.message, count: null };
    }

    return { data, error: null, count };
  } catch (error) {
    console.error(`Unexpected error in getFilteredProductVariants: ${error}`);
    return { data: null, error: "An unexpected error occurred.", count: null };
  }
}

/**
 * Calls the 'get_business_variant_stats' RPC function.
 * @param businessId The ID of the business.
 * @returns An object containing the variant statistics or an error.
 */
export async function getRpcBusinessVariantStats(supabase: SupabaseClient, businessId: string) {
  try {
    const { data, error } = await supabase
      .rpc(RPC_FUNCTIONS.GET_BUSINESS_VARIANT_STATS, { business_uuid: businessId });

    if (error) {
      console.error(`Error calling get_business_variant_stats: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error(`Unexpected error in getRpcBusinessVariantStats: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Calls the 'is_variant_combination_unique' RPC function.
 * @param productId The ID of the product.
 * @param variantValues The variant values to check.
 * @param excludeVariantId Optional. The ID of the variant to exclude from the check.
 * @returns An object indicating whether the variant combination is unique or an error.
 */
export async function getRpcIsVariantCombinationUnique(
  supabase: SupabaseClient,
  productId: string,
  variantValues: Json,
  excludeVariantId?: string
) {
  try {
    const { data: result, error: functionError } = await supabase
      .rpc(RPC_FUNCTIONS.IS_VARIANT_COMBINATION_UNIQUE, {
        product_uuid: productId,
        variant_vals: variantValues,
        exclude_variant_id: excludeVariantId || undefined
      });

    if (functionError) {
      console.error(`Error calling is_variant_combination_unique: ${functionError.message}`);
      return { isUnique: undefined, error: functionError.message };
    }
    return { isUnique: result || undefined, error: null };
  } catch (err) {
    console.error(`Unexpected error in getRpcIsVariantCombinationUnique: ${err}`);
    return { isUnique: undefined, error: "An unexpected error occurred." };
  }
}

/**
 * Fetches products with variant information for a given business.
 * @param userId The ID of the business user.
 * @param page The page number.
 * @param limit The number of items per page.
 * @param filters Product filters.
 * @param sortBy Product sorting criteria.
 * @returns An object containing the products data and count, or an error.
 */
export async function getProductsWithVariantInfo(
  supabase: SupabaseClient,
  userId: string,
  page: number = 1,
  limit: number = 10,
  filters: {
    searchTerm?: string;
    hasVariants?: boolean;
    productType?: string;
  } = {},
  sortBy: "created_asc" | "created_desc" | "price_asc" | "price_desc" | "name_asc" | "name_desc" | "available_first" | "unavailable_first" | "variant_count_asc" | "variant_count_desc" = "created_desc"
) {
  const offset = (page - 1) * limit;

  let query = supabase
    .from(TABLES.PRODUCTS_SERVICES)
    .select(
      `
      ${COLUMNS.ID},
      ${COLUMNS.BUSINESS_ID},
      ${COLUMNS.PRODUCT_TYPE},
      ${COLUMNS.NAME},
      ${COLUMNS.DESCRIPTION},
      ${COLUMNS.BASE_PRICE},
      ${COLUMNS.DISCOUNTED_PRICE},
      ${COLUMNS.IS_AVAILABLE},
      ${COLUMNS.IMAGE_URL},
      ${COLUMNS.IMAGES},
      ${COLUMNS.FEATURED_IMAGE_INDEX},
      ${COLUMNS.CREATED_AT},
      ${COLUMNS.UPDATED_AT},
      ${COLUMNS.SLUG},
      ${TABLES.PRODUCT_VARIANTS}(${COLUMNS.ID}, ${COLUMNS.IS_AVAILABLE})
    `,
      { count: "exact" }
    )
    .eq(COLUMNS.BUSINESS_ID, userId);

  // Apply Filters
  if (filters.searchTerm)
    query = query.or(
      `${COLUMNS.NAME}.ilike.%${filters.searchTerm}%,${COLUMNS.DESCRIPTION}.ilike.%${filters.searchTerm}%`
    );
  if (filters.hasVariants !== undefined) {
    if (filters.hasVariants) {
      // Only products that have variants
      query = query.not(TABLES.PRODUCT_VARIANTS, "is", null);
    } else {
      // Only products that don't have variants
      query = query.is(TABLES.PRODUCT_VARIANTS, null);
    }
  }
  if (filters.productType)
    query = query.eq(COLUMNS.PRODUCT_TYPE, filters.productType);

  // Apply Sorting
  switch (sortBy) {
    case "created_asc":
      query = query.order(COLUMNS.CREATED_AT, { ascending: true });
      break;
    case "price_asc":
      query = query
        .order(COLUMNS.DISCOUNTED_PRICE, { ascending: true, nullsFirst: false })
        .order(COLUMNS.BASE_PRICE, { ascending: true, nullsFirst: false });
      break;
    case "price_desc":
      query = query
        .order(COLUMNS.DISCOUNTED_PRICE, { ascending: false, nullsFirst: false })
        .order(COLUMNS.BASE_PRICE, { ascending: false, nullsFirst: false });
      break;
    case "name_asc":
      query = query.order(COLUMNS.NAME, { ascending: true });
      break;
    case "name_desc":
      query = query.order(COLUMNS.NAME, { ascending: false });
      break;
    case "available_first":
      query = query
        .order(COLUMNS.IS_AVAILABLE, { ascending: false })
        .order(COLUMNS.CREATED_AT, { ascending: false });
      break;
    case "unavailable_first":
      query = query
        .order(COLUMNS.IS_AVAILABLE, { ascending: true })
        .order(COLUMNS.CREATED_AT, { ascending: false });
      break;
    case "created_desc":
    case "variant_count_asc": // Handled in post-processing
    case "variant_count_desc": // Handled in post-processing
    default:
      query = query.order(COLUMNS.CREATED_AT, { ascending: false });
      break;
  }

  query = query.range(offset, offset + limit - 1);
  const { data, error, count } = await query;

  if (error) {
    console.error("Fetch Products Error:", error);
    return { data: null, error: error.message, count: null };
  }

  return { data, count, error: null };
}

/**
 * Fetches details of a product variant by its ID, including associated product's business ID.
 * @param variantId The ID of the product variant.
 * @returns An object containing the variant data or an error.
 */
export async function getVariantDetailsWithProductBusinessId(supabase: SupabaseClient, variantId: string): Promise<{ data: (Tables<'product_variants'> & { products_services: Pick<Tables<'products_services'>, 'business_id'> | null }) | null; error: string | null }> {
  try {
    const { data, error } = await supabase
      .from(TABLES.PRODUCT_VARIANTS)
      .select(`
        *,
        products_services!inner(business_id)
      `)
      .eq(COLUMNS.ID, variantId)
      .single();

    if (error) {
      console.error(`Error fetching variant details by ID: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error(`Unexpected error fetching variant details by ID: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Updates a product variant's data.
 * @param variantId The ID of the variant to update.
 * @param updateData The data to update.
 * @returns An object indicating success or an error.
 */
export async function updateProductVariantData(supabase: SupabaseClient, variantId: string, updateData: TablesUpdate<'product_variants'>) {
  try {
    const { data, error } = await supabase
      .from(TABLES.PRODUCT_VARIANTS)
      .update(updateData)
      .eq(COLUMNS.ID, variantId)
      .select()
      .single();

    if (error) {
      console.error(`Error updating product variant data: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error(`Unexpected error updating product variant data: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Performs a bulk update on product variants.
 * @param updates An array of objects containing variant ID and data to update.
 * @returns An object indicating success or an error.
 */
export async function bulkUpdateProductVariantsData(supabase: SupabaseClient, updates: { id: string; data: TablesUpdate<'product_variants'> }[]) {
  try {
    const updatePromises = updates.map(update =>
      supabase
        .from(TABLES.PRODUCT_VARIANTS)
        .update(update.data)
        .eq(COLUMNS.ID, update.id)
    );

    const results = await Promise.all(updatePromises);
    const errors = results.filter(result => result.error).map(result => result.error?.message);

    if (errors.length > 0) {
      console.error(`Errors during bulk update of product variants: ${errors.join(", ")}`);
      return { success: false, error: `Failed to update some variants: ${errors.join(", ")}` };
    }
    return { success: true, error: null };
  } catch (err) {
    console.error(`Unexpected error during bulk update of product variants: ${err}`);
    return { success: false, error: "An unexpected error occurred." };
  }
}


type ProductVariantWithBusinessId = Tables<'product_variants'> & { products_services: Pick<Tables<'products_services'>, 'business_id'>; };

/**
 * Fetches all product variants for a given product ID.
 * @param productId The ID of the product.
 * @returns An object containing the product variants data or an error.
 */
export async function getVariantsByProductId(supabase: SupabaseClient, productId: string): Promise<{ data: ProductVariantWithBusinessId[] | null; error: string | null }> {
  try {
    const { data, error } = await supabase
      .from(TABLES.PRODUCT_VARIANTS)
      .select(`${COLUMNS.ID}, ${COLUMNS.IS_AVAILABLE}, ${COLUMNS.VARIANT_NAME}, ${COLUMNS.IMAGES}, products_services!inner(${COLUMNS.BUSINESS_ID}))`)
      .eq(COLUMNS.PRODUCT_ID, productId);

    if (error) {
      console.error(`Error fetching variants by product ID: ${error.message}`);
      return { data: null, error: error.message };
    }
    // Explicitly map the data to the correct type
    const typedData: ProductVariantWithBusinessId[] = (data as unknown as (Tables<'product_variants'> & { products_services: Pick<Tables<'products_services'>, 'business_id'>; })[]).map(item => ({
      ...item,
      products_services: item.products_services as Pick<Tables<'products_services'>, 'business_id'>,
    }));
    return { data: typedData, error: null };
  } catch (err) {
    console.error(`Unexpected error fetching variants by product ID: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}


/**
 * Deletes a product variant by its ID.
 * @param variantId The ID of the product variant to delete.
 * @returns An object indicating success or an error.
 */
export async function deleteProductVariantById(supabase: SupabaseClient, variantId: string) {
  try {
    const { error } = await supabase
      .from(TABLES.PRODUCT_VARIANTS)
      .delete()
      .eq(COLUMNS.ID, variantId);

    if (error) {
      console.error(`Error deleting product variant by ID: ${error.message}`);
      return { success: false, error: error.message };
    }
    return { success: true, error: null };
  } catch (err) {
    console.error(`Unexpected error deleting product variant by ID: ${err}`);
    return { success: false, error: "An unexpected error occurred." };
  }
}