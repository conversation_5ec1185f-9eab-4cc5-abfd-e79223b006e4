import { SupabaseClient } from "@supabase/supabase-js";
import { TABLES, COLUMNS, BUCKETS } from "../constants";
import { RealtimePostgresChangesPayload } from "@supabase/supabase-js";

/**
 * Fetches the currently authenticated user.
 * @returns An object containing the user data or an error.
 */
export async function getAuthenticatedUser(supabase: SupabaseClient) {
  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    if (error) {
      console.error(`Error fetching authenticated user: ${error.message}`);
      return { user: null, error: "User not found or authentication error." };
    }
    return { user, error: null };
  } catch (err) {
    console.error(`Unexpected error fetching authenticated user: ${err}`);
    return { user: null, error: "An unexpected error occurred." };
  }
}

/**
 * Uploads a file to Supabase Storage.
 * @param bucketName The name of the storage bucket.
 * @param path The path where the file will be stored in the bucket.
 * @param fileBuffer The file content as a Buffer.
 * @param contentType The content type of the file (e.g., 'image/jpeg').
 * @param upsert Whether to upsert the file if it already exists.
 * @returns An object indicating success or an error.
 */
export async function uploadFileToStorage(
  supabase: SupabaseClient,
  bucketName: string,
  path: string,
  fileBuffer: Buffer,
  contentType: string,
  upsert: boolean = true
) {
  try {
    const { error } = await supabase.storage
      .from(bucketName)
      .upload(path, fileBuffer, {
        contentType,
        upsert,
      });

    if (error) {
      console.error(`Error uploading file to storage: ${error.message}`);
      return { success: false, error: error.message };
    }
    return { success: true, error: null };
  } catch (err) {
    console.error(`Unexpected error uploading file to storage: ${err}`);
    return { success: false, error: "An unexpected error occurred." };
  }
}

/**
 * Retrieves the public URL for a file in Supabase Storage.
 * @param bucketName The name of the storage bucket.
 * @param path The path of the file in the bucket.
 * @returns An object containing the public URL or an error.
 */
export async function getPublicUrlFromStorage(supabase: SupabaseClient, bucketName: string, path: string) {
  try {
    const { data } = supabase.storage.from(bucketName).getPublicUrl(path);
    if (!data?.publicUrl) {
      return { publicUrl: null, error: "Could not retrieve public URL." };
    }
    return { publicUrl: data.publicUrl, error: null };
  } catch (err) {
    console.error(`Unexpected error getting public URL: ${err}`);
    return { publicUrl: null, error: "An unexpected error occurred." };
  }
}

/**
 * Removes files from Supabase Storage.
 * @param bucketName The name of the storage bucket.
 * @param paths An array of file paths to remove from the bucket.
 * @returns An object indicating success or an error.
 */
export async function removeFileFromStorage(supabase: SupabaseClient, bucketName: string, paths: string[]) {
  try {
    const { error } = await supabase.storage.from(bucketName).remove(paths);
    if (error) {
      console.error(`Error removing file from storage: ${error.message}`);
      return { success: false, error: error.message };
    }
    return { success: true, error: null };
  } catch (err) {
    console.error(`Unexpected error removing file from storage: ${err}`);
    return { success: false, error: "An unexpected error occurred." };
  }
}

/**
 * Updates the authenticated user's phone number in Supabase Auth.
 * @param phone The new phone number.
 * @returns An object indicating success or an error.
 */
export async function updateAuthUserPhone(supabase: SupabaseClient, phone: string) {
  try {
    const { error } = await supabase.auth.updateUser({
      phone: `+91${phone}`,
    });

    if (error) {
      console.error(`Error updating auth user phone: ${error.message}`);
      return { success: false, error: error.message };
    }
    return { success: true, error: null };
  } catch (err) {
    console.error(`Unexpected error updating auth user phone: ${err}`);
    return { success: false, error: "An unexpected error occurred." };
  }
}

/**
 * Lists files in a Supabase Storage bucket.
 * @param bucketName The name of the storage bucket.
 * @param path The path within the bucket to list files from.
 * @param options Options for listing files (e.g., limit).
 * @returns An object containing the list of files or an error.
 */
export async function listStorageFiles(
  supabase: SupabaseClient,
  bucketName: string,
  path: string,
  options?: { limit?: number; offset?: number; search?: string }
) {
  try {
    const { data, error } = await supabase.storage
      .from(bucketName)
      .list(path, options);

    if (error) {
      console.error(`Error listing storage files: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error(`Unexpected error listing storage files: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}

/**
 * Signs out the currently authenticated user.
 * @returns An object indicating success or an error.
 */
export async function signOutUser(supabase: SupabaseClient) {
  try {
    const { error } = await supabase.auth.signOut();
    if (error) {
      console.error(`Error signing out user: ${error.message}`);
      return { success: false, error: error.message };
    }
    return { success: true, error: null };
  } catch (err) {
    console.error(`Unexpected error signing out user: ${err}`);
    return { success: false, error: "An unexpected error occurred." };
  }
}

/**
 * Subscribes to real-time changes in a Supabase table.
 * @param tableName The name of the table to subscribe to.
 * @param filter The filter to apply to the subscription (e.g., `id=eq.some_id`).
 * @param callback The callback function to execute when changes are received.
 * @returns A cleanup function to unsubscribe from the channel.
 */
export function subscribeToTableChanges<T extends { [key: string]: any }>(
  supabase: SupabaseClient,
  tableName: string,
  filter: string,
  callback: (payload: RealtimePostgresChangesPayload<T>) => void
): () => void {

  const channel = supabase
    .channel(`public:${tableName}`)
    .on<T>(
      "postgres_changes",
      {
        event: "*",
        schema: "public",
        table: tableName,
        filter: filter,
      },
      callback
    )
    .subscribe();

  return () => {
    supabase.removeChannel(channel);
  };
}

/**
 * Fetches user subscriptions.
 * @param userId The ID of the user.
 * @returns An object containing the subscriptions data or an error.
 */
export async function fetchUserSubscriptions(supabase: SupabaseClient, userId: string) {
  try {
    const { data, error } = await supabase
      .from(TABLES.SUBSCRIPTIONS)
      .select(COLUMNS.BUSINESS_PROFILE_ID)
      .eq(COLUMNS.USER_ID, userId);

    if (error) {
      console.error(`Error fetching user subscriptions: ${error.message}`);
      return { data: null, error: error.message };
    }
    return { data, error: null };
  } catch (err) {
    console.error(`Unexpected error fetching user subscriptions: ${err}`);
    return { data: null, error: "An unexpected error occurred." };
  }
}


/**
 * Fetches the state name for a given city from the pincodes table.
 * @param city The city name to search for.
 * @returns An object containing the state name or an error.
 */
export async function getStateNameByCity(supabase: SupabaseClient, city: string): Promise<{ stateName: string | null; error: string | null }> {
  try {
    const { data, error } = await supabase
      .from(TABLES.PINCODES)
      .select(COLUMNS.STATE_NAME)
      .ilike(COLUMNS.DIVISION_NAME, `%${city}%`)
      .limit(1);

    if (error) {
      console.error(`Error fetching state name for city ${city}: ${error.message}`);
      return { stateName: null, error: error.message };
    }

    return { stateName: data && data.length > 0 ? data[0].StateName : null, error: null };
  } catch (err) {
    console.error(`Unexpected error fetching state name for city ${city}: ${err}`);
    return { stateName: null, error: "An unexpected error occurred." };
  }
}

/**
 * Fetches unified posts with optional filters and pagination.
 * @param from The starting index for pagination.
 * @param to The ending index for pagination.
 * @param conditions Optional array of conditions for filtering.
 * @returns An object containing the unified posts data, count, or an error.
 */
export async function getUnifiedPosts(
  supabase: SupabaseClient,
  from: number,
  to: number,
  conditions: string[] = []
) {
  let query = supabase.from(TABLES.UNIFIED_POSTS).select('*', { count: 'exact' });

  if (conditions.length > 0) {
    query = query.or(conditions.join(','));
  }

  const { data, error, count } = await query
    .order(COLUMNS.CREATED_AT, { ascending: false })
    .range(from, to);

  if (error) {
    console.error("Error fetching unified posts:", error);
    return { data: null, error: error.message, count: null };
  }
  return { data, error: null, count };
}
