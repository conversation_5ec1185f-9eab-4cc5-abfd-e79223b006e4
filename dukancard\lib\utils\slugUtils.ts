"use server";

import { unstable_noStore as noStore } from "next/cache";
import * as z from "zod";
import { checkBusinessSlugUniqueness } from "@/lib/supabase/services/businessService";
import { getAuthenticatedUser } from "@/lib/supabase/services/sharedService";
import { createClient } from "@/utils/supabase/server";

/**
 * Shared utility function to check if a business slug is available
 * Used by both onboarding and dashboard business card edit
 */
export async function checkBusinessSlugAvailability(
  slug: string,
  userId?: string | null
): Promise<{ available: boolean; error?: string }> {
  noStore(); // Ensure this check always hits the database

  // Basic validation
  if (!slug || slug.length < 3) {
    return { available: false, error: "Slug must be at least 3 characters." };
  }

  // Format validation
  const slugFormatCheck = z
    .string()
    .regex(/^[a-z0-9]+(?:-[a-z0-9]+)*$/)
    .safeParse(slug);

  if (!slugFormatCheck.success) {
    return {
      available: false,
      error: "Invalid format (lowercase, numbers, hyphens only).",
    };
  }

  const supabase = await createClient()

  let currentUserId = userId;
  if (!currentUserId) {
    const { user } = await getAuthenticatedUser(supabase);
    currentUserId = user?.id;
  }

  const { available, error } = await checkBusinessSlugUniqueness(supabase, slug, currentUserId);

  if (error) {
    return { available: false, error: `Database error checking slug: ${error}` };
  }

  return { available };
}