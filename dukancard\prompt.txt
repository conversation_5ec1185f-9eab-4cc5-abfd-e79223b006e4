I will provide you with files which you need to read, find supabase usage, and refactor them to @businessService for business related supabase functions, @customerService for customer related supabase functions and @sharedService for common shared supabase functions, where all commonly used and duplicate supabase functions are declared. You also need to add comment to each function to define what each function does. This will greatly reduce redundancy and decouple supabsae from my main source code. Also make sure you repalce string column name table name and bucket name with C:\web-app\dukancard\lib\supabase\constants.ts and use centralized types from C:\web-app\dukancard\types\supabase.ts. If the functions do not exist in service files, then add them and refactor the code. Functions which serve the same purpose should use the same function in the servce files. And after you are done, run npx tsc --noEmit to ensure there are no errors after the changes you made.

I will provide you with files which you need to read, find supabase usage, and refactor them to @businessService for business related supabase functions, @customerService for customer related supabase functions and @sharedService for common shared supabase functions, where all commonly used and duplicate supabase functions are declared. You also need to add comment to each function to define what each function does. This will greatly reduce redundancy and decouple supabsae from my main source code. Also make sure you repalce string column name table name and bucket name with @constants.ts and use centralized types from @types\supabase.ts. If the functions do not exist in service files, then add them and refactor the code. Functions which serve the same purpose should use the same function in the servce files. And after you are done, run npx tsc --noEmit to ensure there are no errors after the changes you made.